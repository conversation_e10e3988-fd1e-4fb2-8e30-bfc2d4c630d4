# Docker Setup for Concentric Cloud React Frontend

This document explains how to run the React frontend alongside your existing PHP/Symfony backend using Docker to solve CORS issues.

## 🎯 Solution Overview

The Docker setup allows your React app to run on the same Docker network as your backend, eliminating CORS issues by:

1. **Shared Network**: Both frontend and backend run on `concentric_network`
2. **Internal Communication**: Frontend can call backend directly via container names
3. **API Proxying**: Vite dev server proxies `/api` requests to the backend
4. **Development Hot Reload**: Source code changes trigger automatic rebuilds

## 📁 Files Created

```
├── Dockerfile                      # Multi-stage build (dev + prod)
├── docker-compose.yml             # Full stack with external backend
├── docker-compose.standalone.yml   # Frontend-only (recommended)
├── nginx.conf                      # Production nginx config
├── .dockerignore                   # Docker build exclusions
├── .env.development                # Local dev environment
├── .env.docker                     # Docker environment
├── scripts/
│   ├── docker-dev.sh              # Linux/Mac startup script
│   └── docker-dev.bat             # Windows startup script
└── DOCKER.md                      # This documentation
```

## 🚀 Quick Start

### Prerequisites

1. Your backend Docker Compose stack must be running
2. The `concentric_network` must exist
3. The `concentric_app` container must be running

### Option 1: Use the Helper Script (Recommended)

**Linux/Mac:**
```bash
./scripts/docker-dev.sh
```

**Windows:**
```batch
scripts\docker-dev.bat
```

### Option 2: Manual Docker Compose

```bash
# Start frontend container
docker-compose -f docker-compose.standalone.yml up --build

# Or run in background
docker-compose -f docker-compose.standalone.yml up -d --build
```

### Option 3: Local Development with API Proxy

```bash
# Install dependencies
pnpm install

# Start development server (will proxy API calls to localhost:8080)
pnpm dev
```

## 🌐 Access Points

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend (Dev)** | http://localhost:3000 | React app with hot reload |
| **Backend API** | http://localhost:8080 | Your existing PHP/Symfony API |
| **Database Admin** | http://localhost:8081 | phpMyAdmin |

## 🔧 Configuration

### Environment Variables

The app supports different API base URLs:

**Local Development (.env.development):**
```env
VITE_API_BASE_URL=http://localhost:8080
```

**Docker Development (.env.docker):**
```env
VITE_API_BASE_URL=http://concentric_app:80
```

### Vite Proxy Configuration

The Vite config automatically proxies API requests:

```typescript
server: {
  proxy: {
    '/api': {
      target: process.env.VITE_API_BASE_URL || 'http://localhost:8080',
      changeOrigin: true,
      secure: false,
    },
  },
}
```

## 🐳 Docker Architecture

### Development Container

- **Base Image**: `node:20-alpine`
- **Package Manager**: pnpm
- **Port**: 3000 (host) → 5173 (container)
- **Volumes**: Source code mounted for hot reload
- **Network**: `concentric_network`

### Production Container

- **Build Stage**: `node:20-alpine` (builds the app)
- **Runtime Stage**: `nginx:alpine` (serves static files)
- **Port**: 3001 (host) → 80 (container)
- **Features**: Gzip compression, security headers, API proxying

## 🔄 API Communication

### Development Flow

```mermaid
graph LR
    A[Browser] --> B[Frontend Container :3000]
    B --> C[Vite Proxy]
    C --> D[Backend Container :80]
    D --> E[Database Container :3306]
```

### Request Path

1. Browser makes request to `http://localhost:3000/api/...`
2. Vite dev server intercepts `/api` requests
3. Proxy forwards to `http://concentric_app:80/api/...`
4. Backend processes request and returns response
5. Response flows back through proxy to browser

## 🛠️ Troubleshooting

### Network Issues

**Problem**: `concentric_network` not found
```bash
# Check if network exists
docker network ls | grep concentric_network

# If missing, start your backend first
cd /path/to/your/backend
docker-compose up -d
```

**Problem**: Cannot connect to backend
```bash
# Check if backend container is running
docker ps | grep concentric_app

# Check container logs
docker logs concentric_app
```

### CORS Issues

If you still see CORS errors:

1. **Check API base URL**: Ensure `VITE_API_BASE_URL` points to correct backend
2. **Verify network**: Both containers must be on `concentric_network`
3. **Check backend CORS**: Your Symfony app may need CORS configuration

### Performance Issues

**Problem**: Slow container startup
```bash
# Use BuildKit for faster builds
export DOCKER_BUILDKIT=1
docker-compose up --build
```

**Problem**: Hot reload not working
```bash
# Check volume mounts
docker-compose -f docker-compose.standalone.yml config

# Restart with clean volumes
docker-compose -f docker-compose.standalone.yml down -v
docker-compose -f docker-compose.standalone.yml up --build
```

## 📊 Resource Usage

The frontend container is configured with reasonable limits:

- **Memory**: 2GB limit, 512MB reservation
- **CPU**: 2 cores limit, 0.5 core reservation
- **Storage**: Cached volumes for better performance

## 🔒 Security Considerations

### Development
- Container runs as non-root user
- Source code is mounted read-only where possible
- API proxy prevents direct backend exposure

### Production
- Multi-stage build minimizes image size
- Nginx security headers enabled
- Static files served with proper caching

## 📝 Additional Commands

### Container Management

```bash
# View logs
docker-compose -f docker-compose.standalone.yml logs -f

# Stop services
docker-compose -f docker-compose.standalone.yml down

# Rebuild without cache
docker-compose -f docker-compose.standalone.yml build --no-cache

# Shell into container
docker exec -it concentric_frontend sh
```

### Cleanup

```bash
# Remove containers and volumes
docker-compose -f docker-compose.standalone.yml down -v

# Remove images
docker rmi concentric_frontend

# Clean up build cache
docker builder prune
```

## 🎉 Success Indicators

When everything is working correctly:

1. ✅ Frontend loads at http://localhost:3000
2. ✅ No CORS errors in browser console
3. ✅ API calls succeed (check Network tab)
4. ✅ Hot reload works when editing React components
5. ✅ Both containers appear in `docker ps`

## 🆘 Getting Help

If you encounter issues:

1. Check the container logs: `docker-compose logs`
2. Verify network connectivity: `docker network inspect concentric_network`
3. Test API directly: `curl http://localhost:8080/api/health`
4. Check browser developer tools for specific errors

The setup is designed to work seamlessly with your existing backend infrastructure while providing a smooth development experience for the React frontend.