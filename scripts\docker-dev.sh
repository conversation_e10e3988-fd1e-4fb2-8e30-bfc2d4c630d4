#!/bin/bash

# Script to run React frontend with <PERSON><PERSON> alongside existing backend

set -e

echo "🚀 Starting Concentric Cloud React Frontend in Docker..."

# Check if backend is accessible
echo "🔍 Checking if backend is accessible at localhost:8080..."
if curl -f -s http://localhost:8080 >/dev/null 2>&1 || curl -f -s http://localhost:8080/api >/dev/null 2>&1; then
    echo "✅ Backend is accessible at localhost:8080"
else
    echo "⚠️  Backend not accessible at localhost:8080"
    echo "Please make sure your backend is running and accessible at http://localhost:8080"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "💡 To start your backend, run: docker-compose up -d in your backend directory"
        exit 1
    fi
fi

# Try to connect to existing network, fall back to simple mode
if docker network ls | grep -q concentric_network && docker ps | grep -q concentric_app; then
    echo "🌐 Using shared Docker network (concentric_network)"
    echo "🏗️  Building and starting frontend container..."
    docker-compose -f docker-compose.standalone.yml up --build
else
    echo "🌐 Using host network mode (connecting to localhost:8080)"
    echo "🏗️  Building and starting frontend container..."
    docker-compose -f docker-compose.simple.yml up --build
fi

echo "✅ Frontend should be available at http://localhost:3000"
echo "📡 API requests will be proxied to your backend"