import { useState } from 'react';
import { 
  LayoutGrid, 
  List, 
  TrendingUp 
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';

import { LeadPipeline, LeadScoring } from '@/features/customerops/components';

export function CustomerOpsLeadsPage() {
  const [viewMode, setViewMode] = useState<'pipeline' | 'list' | 'scoring'>('pipeline');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Lead Management</h1>
          <p className="text-lg text-muted-foreground">
            Manage and track leads through your sales funnel
          </p>
        </div>
        <div className="flex rounded-lg border bg-background p-1">
          <Button
            variant={viewMode === 'pipeline' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('pipeline')}
          >
            <LayoutGrid className="mr-2 h-4 w-4" />
            Pipeline
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="mr-2 h-4 w-4" />
            List
          </Button>
          <Button
            variant={viewMode === 'scoring' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('scoring')}
          >
            <TrendingUp className="mr-2 h-4 w-4" />
            Scoring
          </Button>
        </div>
      </div>

      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'pipeline' | 'list' | 'scoring')}>
        <TabsList className="hidden">
          <TabsTrigger value="pipeline">Pipeline</TabsTrigger>
          <TabsTrigger value="list">List</TabsTrigger>
          <TabsTrigger value="scoring">Scoring</TabsTrigger>
        </TabsList>

        <TabsContent value="pipeline" className="space-y-4">
          <LeadPipeline />
        </TabsContent>

        <TabsContent value="list" className="space-y-4">
          <div className="text-center text-muted-foreground py-8">
            Lead List component coming soon...
          </div>
        </TabsContent>

        <TabsContent value="scoring" className="space-y-4">
          <LeadScoring />
        </TabsContent>
      </Tabs>
    </div>
  );
}