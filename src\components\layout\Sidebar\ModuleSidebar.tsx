import { useState } from 'react';
import { Link, useLocation } from 'react-router';
import { 
  Menu, 
  <PERSON>, 
  Settings, 
  Clock, 
  DollarSign, 
  Wrench,
  BarChart3,
  Building,
  UserCheck,
  TrendingUp,
  ShoppingCart,
  Package,
  Ticket,
  Timer,
  FileText,
  PieChart,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@/components/ui/sheet';
import { usePermissions } from '@/features/auth/hooks/usePermissions';
import { cn } from '@/lib/utils';

const moduleConfig = [
  {
    id: 'customerops',
    title: 'CustomerOps',
    icon: Users,
    permission: 'customerops:read',
    items: [
      { title: 'Dashboard', href: '/customerops', icon: BarChart3 },
      { title: 'Customers', href: '/customerops/customers', icon: Building },
      { title: 'Leads', href: '/customerops/leads', icon: User<PERSON>heck },
      { title: 'Deals', href: '/customerops/deals', icon: TrendingUp },
      { title: 'Quotes', href: '/customerops/quotes', icon: ShoppingCart },
    ]
  },
  {
    id: 'serviceops',
    title: 'ServiceOps',
    icon: Wrench,
    permission: 'serviceops:read',
    items: [
      { title: 'Dashboard', href: '/serviceops', icon: BarChart3 },
      { title: 'Assets', href: '/serviceops/assets', icon: Package },
      { title: 'Tickets', href: '/serviceops/tickets', icon: Ticket },
      { title: 'Assignments', href: '/serviceops/assignments', icon: UserCheck },
    ]
  },
  {
    id: 'timesheets',
    title: 'Timesheets',
    icon: Clock,
    permission: 'timesheets:read',
    items: [
      { title: 'My Timesheet', href: '/timesheets', icon: Timer },
      { title: 'Team Timesheets', href: '/timesheets/team', icon: Users },
      { title: 'Reports', href: '/timesheets/reports', icon: FileText },
    ]
  },
  {
    id: 'accountops',
    title: 'AccountOps',
    icon: DollarSign,
    permission: 'accountops:read',
    items: [
      { title: 'Dashboard', href: '/accountops', icon: PieChart },
      { title: 'Invoices', href: '/accountops/invoices', icon: FileText },
      { title: 'Expenses', href: '/accountops/expenses', icon: DollarSign },
      { title: 'Budgets', href: '/accountops/budgets', icon: TrendingUp },
    ]
  },
  {
    id: 'system',
    title: 'System',
    icon: Settings,
    permission: 'system:admin',
    items: [
      { title: 'Users', href: '/system/users', icon: Users },
      { title: 'Roles', href: '/system/roles', icon: UserCheck },
      { title: 'Entities', href: '/system/entities', icon: Building },
      { title: 'Settings', href: '/system/settings', icon: Settings },
    ]
  }
];

interface SidebarContentProps {
  className?: string;
}

function SidebarContent({ className }: SidebarContentProps) {
  const location = useLocation();
  const { canAccessModule } = usePermissions();
  const [expandedModules, setExpandedModules] = useState<string[]>([]);

  const visibleModules = moduleConfig.filter(module => 
    canAccessModule(module.id)
  );

  const toggleModule = (moduleId: string) => {
    setExpandedModules(prev => 
      prev.includes(moduleId) 
        ? prev.filter(id => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  // Auto-expand current module
  const currentModule = visibleModules.find(module => 
    location.pathname.startsWith(`/${module.id}`)
  );

  if (currentModule && !expandedModules.includes(currentModule.id)) {
    setExpandedModules(prev => [...prev, currentModule.id]);
  }

  return (
    <div className={cn("flex flex-col", className)}>
      <div className="p-6">
        <h2 className="text-lg font-semibold">Concentric Cloud</h2>
        <p className="text-sm text-muted-foreground">Platform Navigation</p>
      </div>
      
      <ScrollArea className="flex-1 px-3">
        <div className="space-y-2">
          {/* Dashboard Link */}
          <Link
            to="/dashboard"
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
              location.pathname === '/dashboard' && "bg-accent text-accent-foreground"
            )}
          >
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </Link>

          {/* Module Navigation */}
          {visibleModules.map((module) => {
            const isExpanded = expandedModules.includes(module.id);
            const isCurrentModule = location.pathname.startsWith(`/${module.id}`);
            
            return (
              <div key={module.id} className="space-y-1">
                <button
                  onClick={() => toggleModule(module.id)}
                  className={cn(
                    "flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
                    isCurrentModule && "bg-accent text-accent-foreground"
                  )}
                >
                  <module.icon className="h-4 w-4" />
                  {module.title}
                  {isExpanded ? (
                    <ChevronDown className="ml-auto h-4 w-4" />
                  ) : (
                    <ChevronRight className="ml-auto h-4 w-4" />
                  )}
                </button>
                
                {isExpanded && (
                  <div className="ml-6 space-y-1">
                    {module.items.map((item) => (
                      <Link
                        key={item.href}
                        to={item.href}
                        className={cn(
                          "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors hover:bg-accent hover:text-accent-foreground",
                          location.pathname === item.href && "bg-accent text-accent-foreground"
                        )}
                      >
                        <item.icon className="h-4 w-4" />
                        {item.title}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
}

export function ModuleSidebar() {
  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden md:block w-64 border-r bg-background">
        <SidebarContent />
      </div>

      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="md:hidden"
          >
            <Menu className="h-4 w-4" />
            <span className="sr-only">Toggle navigation</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-64">
          <SidebarContent />
        </SheetContent>
      </Sheet>
    </>
  );
}