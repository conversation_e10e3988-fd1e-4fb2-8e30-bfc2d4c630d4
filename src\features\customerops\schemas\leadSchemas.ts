import { z } from "zod";

// Lead Status Enum
export const leadStatusSchema = z.enum([
  'new',
  'contacted',
  'qualified',
  'proposal',
  'negotiation',
  'won',
  'lost',
  'nurturing'
], {
  required_error: "Lead status is required",
});

export type LeadStatus = z.infer<typeof leadStatusSchema>;

// Lead Source Enum
export const leadSourceSchema = z.enum([
  'website',
  'social_media',
  'referral',
  'cold_call',
  'email_campaign',
  'trade_show',
  'advertisement',
  'partnership',
  'content_marketing',
  'webinar',
  'other'
], {
  required_error: "Lead source is required",
});

export type LeadSource = z.infer<typeof leadSourceSchema>;

// Lead Score Enum
export const leadScoreSchema = z.enum(['hot', 'warm', 'cold'], {
  required_error: "Lead score is required",
});

export type LeadScore = z.infer<typeof leadScoreSchema>;

// Lead Activity Schema
export const leadActivitySchema = z.object({
  id: z.string(),
  leadId: z.string(),
  type: z.enum(['call', 'email', 'meeting', 'note', 'task', 'status_change']),
  subject: z.string(),
  description: z.string().optional(),
  userId: z.string(),
  userName: z.string(),
  scheduledAt: z.string().optional(),
  completedAt: z.string().optional(),
  createdAt: z.string(),
});

export type LeadActivity = z.infer<typeof leadActivitySchema>;

// Lead Schema
export const leadSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // Basic Information
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  company: z.string().optional(),
  jobTitle: z.string().optional(),
  
  // Lead Details
  status: leadStatusSchema,
  source: leadSourceSchema,
  score: leadScoreSchema,
  estimatedValue: z.number().min(0).optional(),
  estimatedCloseDate: z.string().optional(),
  
  // Address
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().optional(),
  }).optional(),
  
  // Business Details
  industry: z.string().optional(),
  companySize: z.string().optional(),
  website: z.string().url("Invalid website URL").optional(),
  
  // Qualification
  budget: z.number().min(0).optional(),
  authority: z.enum(['decision_maker', 'influencer', 'gatekeeper', 'unknown']).optional(),
  need: z.string().optional(),
  timeline: z.enum(['immediate', 'within_month', 'within_quarter', 'within_year', 'unknown']).optional(),
  
  // Assignment
  assignedUserId: z.string().optional(),
  assignedUserName: z.string().optional(),
  
  // Conversion
  isConverted: z.boolean().default(false),
  convertedCustomerId: z.string().optional(),
  convertedAt: z.string().optional(),
  
  // Metadata
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  lastContactDate: z.string().optional(),
  nextFollowUpDate: z.string().optional(),
  
  // Activities
  activities: z.array(leadActivitySchema).default([]),
  
  // Timestamps
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type Lead = z.infer<typeof leadSchema>;

// Lead Form Schemas
export const leadFormSchema = leadSchema.omit({
  id: true,
  activities: true,
  isConverted: true,
  convertedCustomerId: true,
  convertedAt: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  entityId: z.string().min(1, "Entity is required"),
  tags: z.array(z.string()),
});

export type LeadFormValues = z.infer<typeof leadFormSchema>;

// Lead Activity Form Schema
export const leadActivityFormSchema = leadActivitySchema.omit({
  id: true,
  leadId: true,
  userId: true,
  userName: true,
  createdAt: true,
});

export type LeadActivityFormValues = z.infer<typeof leadActivityFormSchema>;

// Lead Search and Filter Schemas
export const leadSearchSchema = z.object({
  query: z.string().optional(),
  status: leadStatusSchema.optional(),
  source: leadSourceSchema.optional(),
  score: leadScoreSchema.optional(),
  assignedUserId: z.string().optional(),
  entityId: z.string().optional(),
  isConverted: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  sortBy: z.enum(['firstName', 'lastName', 'company', 'createdAt', 'updatedAt', 'estimatedValue', 'estimatedCloseDate']).default('updatedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

export type LeadSearchParams = z.infer<typeof leadSearchSchema>;

// Lead List Response Schema
export const leadListResponseSchema = z.object({
  leads: z.array(leadSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

export type LeadListResponse = z.infer<typeof leadListResponseSchema>;

// Lead Conversion Schema
export const leadConversionSchema = z.object({
  leadId: z.string(),
  customerData: z.object({
    name: z.string().min(2, "Customer name is required"),
    type: z.enum(['individual', 'business']),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    website: z.string().url().optional(),
    address: z.object({
      street: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      zipCode: z.string().optional(),
      country: z.string().optional(),
    }).optional(),
    industry: z.string().optional(),
    companySize: z.string().optional(),
  }),
});

export type LeadConversionValues = z.infer<typeof leadConversionSchema>;

// Lead Pipeline Stats Schema
export const leadPipelineStatsSchema = z.object({
  totalLeads: z.number(),
  newLeads: z.number(),
  qualifiedLeads: z.number(),
  convertedLeads: z.number(),
  conversionRate: z.number(),
  averageTimeToConversion: z.number(), // in days
  totalEstimatedValue: z.number(),
  statuses: z.array(z.object({
    status: leadStatusSchema,
    count: z.number(),
    percentage: z.number(),
  })),
  sources: z.array(z.object({
    source: leadSourceSchema,
    count: z.number(),
    percentage: z.number(),
  })),
});

export type LeadPipelineStats = z.infer<typeof leadPipelineStatsSchema>;