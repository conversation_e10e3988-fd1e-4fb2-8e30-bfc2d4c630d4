import { createContext } from 'react';
import { LoginFormValues, User } from './schemas/authSchemas';

export interface LoginResponse {
  status: number;
  message: string;
  token: string | null;
}

export interface AuthContextType {
  authToken: string | null;
  user: User | null;
  login: (
    credentials: LoginFormValues,
    options?: {
      onSuccessNavigate?: () => void;
      onMfaRequired?: () => void;
    }
  ) => void;
  submitMfa: (mfaToken: string) => void;
  cancelMfa: () => void;
  logout: (navigate?: () => void) => void;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isLoggedIn: boolean;
  isMfaRequired: boolean;
  message: string | null;
}

const AuthContext = createContext<AuthContextType>({
  authToken: null,
  user: null,
  login: () => {},
  submitMfa: () => {},
  cancelMfa: () => {},
  logout: () => {},
  isLoading: false,
  isError: false,
  error: null,
  isLoggedIn: false,
  isMfaRequired: false,
  message: null,
});

export default AuthContext;
