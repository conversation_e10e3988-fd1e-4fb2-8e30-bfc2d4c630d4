import { useContext } from 'react';
import { useNavigate } from 'react-router';
import AuthContext from '../AuthContext';
import { LoginFormValues, MfaFormValues } from '../schemas/authSchemas';

export const useAuthForm = () => {
  const {
    login,
    submitMfa,
    cancelMfa,
    isLoading,
    isError,
    error,
    isMfaRequired,
  } = useContext(AuthContext);
  const navigate = useNavigate();

  const handleLoginSubmit = (values: LoginFormValues) => {
    login(values, {
      onSuccessNavigate: () => navigate('/dashboard'),      
    });
  };

  const handleMfaSubmit = (values: MfaFormValues) => {
    submitMfa(values.mfa_code);
  };

  const handleCancelMfa = () => {
    cancelMfa();
  };

  return {
    handleLoginSubmit,
    handleMfaSubmit,
    handleCancelMfa,
    isLoading,
    isError,
    error,
    isMfaRequired,
  };
};
