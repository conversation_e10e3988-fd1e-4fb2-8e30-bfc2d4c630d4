version: '3.8'

services:
  # React Frontend Development Service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: concentric_frontend
    restart: unless-stopped
    ports:
      - '3000:5173'  # Access frontend at http://localhost:3000
    volumes:
      # Bind mount source code for hot reload
      - .:/app:cached
      - /app/node_modules  # Anonymous volume for node_modules
      - /app/.pnpm-store   # Anonymous volume for pnpm store
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://host.docker.internal:8080
    networks:
      - concentric_network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'

networks:
  # Connect to existing network created by your backend
  concentric_network:
    external: true