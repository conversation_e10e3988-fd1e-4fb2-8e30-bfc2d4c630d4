import { Link } from 'react-router';
import { LucideIcon } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface ModuleCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  href: string;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  badge?: string;
  stats?: {
    label: string;
    value: string | number;
  }[];
}

const colorClasses = {
  blue: 'border-blue-200 bg-blue-50 hover:bg-blue-100 text-blue-900',
  green: 'border-green-200 bg-green-50 hover:bg-green-100 text-green-900',
  purple: 'border-purple-200 bg-purple-50 hover:bg-purple-100 text-purple-900',
  orange: 'border-orange-200 bg-orange-50 hover:bg-orange-100 text-orange-900',
  red: 'border-red-200 bg-red-50 hover:bg-red-100 text-red-900',
};

export function ModuleCard({ 
  title, 
  description, 
  icon: Icon, 
  href, 
  color = 'blue',
  badge,
  stats 
}: ModuleCardProps) {
  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-lg cursor-pointer",
      colorClasses[color]
    )}>
      <Link to={href} className="block">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-white shadow-sm">
                <Icon className="h-6 w-6" />
              </div>
              <div>
                <CardTitle className="text-lg">{title}</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {description}
                </p>
              </div>
            </div>
            {badge && (
              <Badge variant="secondary" className="ml-2">
                {badge}
              </Badge>
            )}
          </div>
        </CardHeader>
        {stats && stats.length > 0 && (
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 gap-4">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <div className="text-xs text-muted-foreground">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        )}
      </Link>
    </Card>
  );
}