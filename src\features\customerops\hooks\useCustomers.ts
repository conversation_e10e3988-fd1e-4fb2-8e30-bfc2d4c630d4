import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import {
  Customer,
  CustomerFormValues,
  CustomerSearchParams,
  CustomerListResponse,
  customerListResponseSchema,
  customerSchema,
} from '../schemas/customerSchemas';

const CUSTOMERS_QUERY_KEY = 'products.customer';

// Query hooks
export const useCustomers = (searchParams?: CustomerSearchParams) => {
  return useQuery({
    queryKey: [CUSTOMERS_QUERY_KEY, searchParams],
    queryFn: async (): Promise<CustomerListResponse> => {
      const params = new URLSearchParams();
      
      // Map frontend parameters to API parameters
      if (searchParams) {
        // Map search query to 'search' parameter
        if (searchParams.query) {
          params.append('search', searchParams.query);
        }
        
        // Map customer type to contact_type_id (if needed, adjust based on API)
        if (searchParams.type === 'business') {
          params.append('contact_type_id', '1'); // Assuming 1 = business
        } else if (searchParams.type === 'individual') {
          params.append('contact_type_id', '0'); // Assuming 0 = individual/contact
        }
        
        // Map account manager if provided
        if (searchParams.accountManagerId) {
          params.append('account_manager', searchParams.accountManagerId);
        }
        
        // Map pagination
        if (searchParams.page) {
          params.append('page', searchParams.page.toString());
        }
        if (searchParams.limit) {
          params.append('display', searchParams.limit.toString());
        }
        
        // Map sorting
        if (searchParams.sortBy) {
          // Map frontend sort fields to API fields
          const apiSortField = searchParams.sortBy === 'updatedAt' ? 'CreatedAt' : searchParams.sortBy;
          params.append('order_by', apiSortField);
        }
        if (searchParams.sortOrder) {
          params.append('order_direction', searchParams.sortOrder.toUpperCase());
        }
      }
      
      const response = await apiClient.request<any>(
        `/api/product.customerops/contacts?${params.toString()}`
      );
      
      // Handle different API response structures
      let customers: any[] = [];
      let pagination: any = {};
      
      if (Array.isArray(response)) {
        // API returns array directly
        customers = response;
      } else if (response.status !== undefined) {
        // API returns: { status: 0, data: [...], pagination?: {...} }
        if (response.status !== 0) {
          throw new Error(response.message || 'API request failed');
        }
        customers = response.data || [];
        pagination = response.pagination || {};
      } else if (response.data) {
        // API returns: { data: [...], pagination?: {...} }
        customers = response.data;
        pagination = response.pagination || {};
      } else {
        // Fallback: treat entire response as data
        customers = response;
      }
      
      // Debug: Log the actual API response structure
      console.log('API Response Debug:', {
        responseStructure: response,
        customersArray: customers,
        firstCustomer: customers[0],
        customersLength: customers.length
      });
      
      // If no customers, return empty array directly
      if (!customers || customers.length === 0) {
        return {
          customers: [],
          total: 0,
          page: 1,
          limit: searchParams?.limit || 20,
          totalPages: 0,
        };
      }
      
      // Transform API contact data to our customer format
      const transformedCustomers = customers.map((contact: any, index: number) => {
        // More defensive ID extraction
        const id = contact.guid || 
                   contact.id || 
                   contact.ContactGUID || 
                   contact.GUID ||
                   contact.Id ||
                   contact.ID ||
                   `fallback-${index}-${Date.now()}`;
        
        // Defensive name construction
        let customerName = contact.AccountName || contact.name || contact.CompanyName;
        if (!customerName) {
          const firstName = contact.FirstName || contact.firstName || '';
          const lastName = contact.LastName || contact.lastName || '';
          customerName = `${firstName} ${lastName}`.trim();
        }
        if (!customerName) {
          customerName = contact.EmailAddress || contact.email || `Customer ${index + 1}`;
        }
        
        // Defensive type mapping
        let customerType: 'business' | 'individual' = 'individual';
        if (contact.AccountType === 1 || contact.AccountType === '1') {
          customerType = 'business';
        } else if (contact.AccountType === 0 || contact.AccountType === '0') {
          customerType = 'individual';
        }
        
        const transformedCustomer = {
          id: String(id), // Ensure it's always a string
          entityId: String(contact.entityId || contact.EntityID || contact.entityid || '1'),
          name: customerName,
          type: customerType,
          status: 'active' as const, // Default status, adjust based on API data
          email: contact.EmailAddress || contact.email || contact.Email || undefined,
          phone: contact.PhoneNumber || contact.phone || contact.Phone || undefined,
          website: contact.website || undefined,
          address: {
            street: contact.address?.street || undefined,
            city: contact.address?.city || undefined,
            state: contact.address?.state || undefined,
            zipCode: contact.address?.zipCode || undefined,
            country: contact.address?.country || undefined,
          },
          industry: contact.industry || undefined,
          companySize: contact.companySize || undefined,
          taxId: contact.taxId || undefined,
          parentCustomerId: contact.parent_contact || undefined,
          accountManagerId: contact.account_manager || undefined,
          tags: Array.isArray(contact.tags) ? contact.tags : [],
          notes: contact.notes || '',
          contacts: [],
          createdAt: contact.CreatedAt || contact.createdAt || new Date().toISOString(),
          updatedAt: contact.UpdatedAt || contact.updatedAt || new Date().toISOString(),
          totalRevenue: Number(contact.totalRevenue) || 0,
          lastContactDate: contact.lastContactDate || undefined,
          acquisitionSource: contact.acquisitionSource || undefined,
        };
        
        // Debug: Log the transformation for the first customer
        if (index === 0) {
          console.log('Transformation Debug:', {
            originalContact: contact,
            extractedId: id,
            transformedCustomer
          });
        }
        
        return transformedCustomer;
      });
      
      const transformedResponse = {
        customers: transformedCustomers,
        total: pagination.total_items || customers.length,
        page: pagination.current_page || 1,
        limit: searchParams?.limit || 20,
        totalPages: pagination.total_pages || Math.ceil((pagination.total_items || customers.length) / (searchParams?.limit || 20)),
      };
      
      // Temporarily bypass Zod validation for debugging
      console.log('Final transformed response:', transformedResponse);
      try {
        return customerListResponseSchema.parse(transformedResponse);
      } catch (error) {
        console.error('Zod validation error:', error);
        // Return the response anyway for debugging
        return transformedResponse as any;
      }
    },
  });
};

export const useCustomer = (customerId: string) => {
  return useQuery({
    queryKey: [CUSTOMERS_QUERY_KEY, customerId],
    queryFn: async (): Promise<Customer> => {
      const response = await apiClient.request<Customer>(
        `/api/product.customerops/contacts/${customerId}`
      );
      return customerSchema.parse(response);
    },
    enabled: !!customerId,
  });
};

// Mutation hooks
export const useCreateCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (customer: CustomerFormValues): Promise<Customer> => {
      const response = await apiClient.request<Customer>('/api/product.customerops/new', {
        method: 'POST',
        body: JSON.stringify(customer),
      });
      return customerSchema.parse(response);
    },
    onSuccess: (newCustomer) => {
      // Invalidate and refetch customers list
      queryClient.invalidateQueries({ queryKey: [CUSTOMERS_QUERY_KEY] });
      
      // Add the new customer to the cache
      queryClient.setQueryData([CUSTOMERS_QUERY_KEY, newCustomer.id], newCustomer);
    },
  });
};

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      customerId, 
      customer 
    }: { 
      customerId: string; 
      customer: Partial<CustomerFormValues> 
    }): Promise<Customer> => {
      const response = await apiClient.request<Customer>(`/api/product.customerops/contacts/${customerId}`, {
        method: 'PUT',
        body: JSON.stringify(customer),
      });
      return customerSchema.parse(response);
    },
    onSuccess: (updatedCustomer) => {
      // Update the customer in the cache
      queryClient.setQueryData([CUSTOMERS_QUERY_KEY, updatedCustomer.id], updatedCustomer);
      
      // Invalidate the customers list to ensure it's fresh
      queryClient.invalidateQueries({ queryKey: [CUSTOMERS_QUERY_KEY] });
    },
  });
};

export const useDeleteCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (customerId: string): Promise<void> => {
      await apiClient.request(`/api/product.customerops/contacts/${customerId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: (_, customerId) => {
      // Remove the customer from the cache
      queryClient.removeQueries({ queryKey: [CUSTOMERS_QUERY_KEY, customerId] });
      
      // Invalidate the customers list
      queryClient.invalidateQueries({ queryKey: [CUSTOMERS_QUERY_KEY] });
    },
  });
};

// Search and export functionality
export const useSearchCustomers = (query: string) => {
  return useQuery({
    queryKey: [CUSTOMERS_QUERY_KEY, 'search', query],
    queryFn: async (): Promise<Customer[]> => {
      if (!query.trim()) return [];
      
      const response = await apiClient.request<Customer[]>(
        `/api/product.customerops/contacts?search=${encodeURIComponent(query)}`
      );
      
      return response.map(customer => customerSchema.parse(customer));
    },
    enabled: query.length > 2, // Only search when query is at least 3 characters
  });
};

export const useExportCustomers = () => {
  return useMutation({
    mutationFn: async (searchParams?: CustomerSearchParams): Promise<Blob> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await fetch(`/api/product.customerops/contacts/export?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-AUTH-TOKEN': localStorage.getItem('authToken') || '',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Export failed: ${response.status}`);
      }
      
      return response.blob();
    },
  });
};