import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Mail, 
  Phone, 
  User, 
  Star,
  StarOff
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import {
  Contact,
  ContactFormValues,
  contactFormSchema,
} from '../schemas/customerSchemas';

interface ContactManagerProps {
  contacts: Contact[];
  onAddContact: (contact: ContactFormValues) => Promise<void>;
  onUpdateContact: (contactId: string, contact: Partial<ContactFormValues>) => Promise<void>;
  onDeleteContact: (contactId: string) => Promise<void>;
  onSetPrimaryContact: (contactId: string) => Promise<void>;
  isLoading?: boolean;
}

export function ContactManager({
  contacts,
  onAddContact,
  onUpdateContact,
  onDeleteContact,
  onSetPrimaryContact,
  isLoading = false,
}: ContactManagerProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [contactToDelete, setContactToDelete] = useState<Contact | null>(null);

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      position: '',
      isPrimary: false,
      isActive: true,
    },
  });

  const openDialog = (contact?: Contact) => {
    if (contact) {
      setEditingContact(contact);
      form.reset({
        firstName: contact.firstName,
        lastName: contact.lastName,
        email: contact.email || '',
        phone: contact.phone || '',
        position: contact.position || '',
        isPrimary: contact.isPrimary,
        isActive: contact.isActive,
      });
    } else {
      setEditingContact(null);
      form.reset({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        position: '',
        isPrimary: contacts.length === 0, // First contact is primary by default
        isActive: true,
      });
    }
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
    setEditingContact(null);
    form.reset();
  };

  const onSubmit = async (data: ContactFormValues) => {
    try {
      if (editingContact) {
        await onUpdateContact(editingContact.id, data);
      } else {
        await onAddContact(data);
      }
      closeDialog();
    } catch (error) {
      console.error('Failed to save contact:', error);
    }
  };

  const handleDelete = async () => {
    if (!contactToDelete) return;
    
    try {
      await onDeleteContact(contactToDelete.id);
      setDeleteDialogOpen(false);
      setContactToDelete(null);
    } catch (error) {
      console.error('Failed to delete contact:', error);
    }
  };

  const handleSetPrimary = async (contactId: string) => {
    try {
      await onSetPrimaryContact(contactId);
    } catch (error) {
      console.error('Failed to set primary contact:', error);
    }
  };

  const primaryContact = contacts.find(contact => contact.isPrimary);

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Contacts</h3>
          <p className="text-sm text-muted-foreground">
            Manage contact persons for this customer
          </p>
        </div>
        <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
          <Button onClick={() => openDialog()}>
            <Plus className="mr-2 h-4 w-4" />
            Add Contact
          </Button>
        </RoleGuard>
      </div>

      {/* Primary Contact Card */}
      {primaryContact && (
        <Card className="border-primary/20 bg-primary/5">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Star className="h-4 w-4 text-primary" />
              Primary Contact
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <div className="font-medium">
                  {primaryContact.firstName} {primaryContact.lastName}
                </div>
                {primaryContact.position && (
                  <div className="text-sm text-muted-foreground">
                    {primaryContact.position}
                  </div>
                )}
                <div className="flex items-center gap-4 text-sm">
                  {primaryContact.email && (
                    <div className="flex items-center gap-1">
                      <Mail className="h-3 w-3" />
                      <a href={`mailto:${primaryContact.email}`} className="text-primary hover:underline">
                        {primaryContact.email}
                      </a>
                    </div>
                  )}
                  {primaryContact.phone && (
                    <div className="flex items-center gap-1">
                      <Phone className="h-3 w-3" />
                      <a href={`tel:${primaryContact.phone}`} className="text-primary hover:underline">
                        {primaryContact.phone}
                      </a>
                    </div>
                  )}
                </div>
              </div>
              <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => openDialog(primaryContact)}
                >
                  <Edit className="h-3 w-3" />
                </Button>
              </RoleGuard>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Contacts Table */}
      {contacts.length > 0 ? (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Contact Info</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {contacts.map((contact) => (
                  <TableRow key={contact.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="p-1.5 rounded-full bg-muted">
                          <User className="h-3 w-3" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {contact.firstName} {contact.lastName}
                          </div>
                          {contact.isPrimary && (
                            <Badge variant="outline" className="text-xs">
                              Primary
                            </Badge>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {contact.position || '-'}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1 text-sm">
                        {contact.email && (
                          <div className="flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            <a href={`mailto:${contact.email}`} className="text-primary hover:underline">
                              {contact.email}
                            </a>
                          </div>
                        )}
                        {contact.phone && (
                          <div className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            <a href={`tel:${contact.phone}`} className="text-primary hover:underline">
                              {contact.phone}
                            </a>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={contact.isActive ? 'default' : 'secondary'}>
                        {contact.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {!contact.isPrimary && (
                          <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSetPrimary(contact.id)}
                              title="Set as primary"
                            >
                              <StarOff className="h-3 w-3" />
                            </Button>
                          </RoleGuard>
                        )}
                        <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openDialog(contact)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                        </RoleGuard>
                        <RoleGuard permissions={[PERMISSIONS['customerops:delete']]}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setContactToDelete(contact);
                              setDeleteDialogOpen(true);
                            }}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </RoleGuard>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-muted-foreground">
              No contacts added yet. Create the first contact to get started.
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add/Edit Contact Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingContact ? 'Edit Contact' : 'Add New Contact'}
            </DialogTitle>
            <DialogDescription>
              {editingContact 
                ? 'Update the contact information below.'
                : 'Enter the contact information for this customer.'
              }
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="John" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="position"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Position</FormLabel>
                    <FormControl>
                      <Input placeholder="CEO, Manager, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-center space-x-4">
                <FormField
                  control={form.control}
                  name="isPrimary"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-x-2">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className="!mt-0">Primary Contact</FormLabel>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-x-2">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className="!mt-0">Active</FormLabel>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={closeDialog}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading 
                    ? (editingContact ? 'Updating...' : 'Adding...') 
                    : (editingContact ? 'Update Contact' : 'Add Contact')
                  }
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Contact</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{contactToDelete?.firstName} {contactToDelete?.lastName}"? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete}
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}