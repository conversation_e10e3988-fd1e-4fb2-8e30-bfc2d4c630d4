## FEATURE:

# Concentric Cloud Platform - Feature Documentation

## Overview
Concentric Cloud is a comprehensive business management platform built with Symfony, designed for multi-tenant organizations. The platform provides integrated solutions for customer relationship management, service operations, financial management, and workflow automation.

## Platform Architecture

### Multi-Tenancy
- **Entity-based isolation**: Each organization operates within its own entity context
- **Branch server architecture**: Supports distributed organizational structures
- **HQ vs Branch modes**: Centralized management with distributed operations

### Security & Access Control
- **Role-based access control (RBAC)**: Granular permission management
- **Multi-factor authentication**: Enhanced security with MFA support
- **API key management**: Secure external integrations
- **Privacy framework**: Field-level security and data protection

## Core Business Modules

### 1. CustomerOps - Customer Relationship Management

**Main Features:**
- **Contact & Customer Management**
  - Comprehensive contact database with detailed profiles
  - Account hierarchy support (parent/child relationships)
  - Customer type categorization and segmentation
  - Account manager assignments and relationship tracking

- **Lead Management**
  - Lead capture and qualification workflows
  - Lead-to-customer conversion tracking
  - Source attribution and campaign tracking
  - Lead scoring and prioritization

- **Call Management**
  - Phone call logging and history tracking
  - Call outcome recording and follow-up scheduling
  - Integration with customer records
  - Call activity reporting

- **Deal & Quote Management**
  - Sales pipeline management with stages
  - Quote generation and approval workflows
  - Deal probability and value tracking
  - Quote line items with pricing and taxes

- **Meeting Management**
  - Meeting scheduling and calendar integration
  - Meeting participant tracking
  - Meeting notes and action items
  - Follow-up task generation

**Key Pages:**
- `/app/product.customerops/` - Main customer dashboard
- `/app/product.customerops/customers` - Customer listing and management
- `/app/product.customerops/leads` - Lead management interface
- `/app/product.customerops/calls` - Call logging and history
- `/app/product.customerops/deals` - Deal pipeline management
- `/app/product.customerops/meetings` - Meeting scheduler

**UI Features:**
- Tabbed customer detail forms with overview, assets, and tickets
- HTMX-powered dynamic lists for real-time updates
- Searchable dropdown filters and advanced search
- Inline field editing with privacy controls
- State management with workflow transitions

### 2. ServiceOps - Service Management & Asset Tracking

**Main Features:**
- **Fixed Asset Management**
  - Complete asset lifecycle tracking from acquisition to disposal
  - Asset categorization with hierarchical organization
  - Assignment tracking with due dates and return notifications
  - Asset history and audit trails

- **Asset Categories & Types**
  - Hierarchical asset organization system
  - Custom asset types with specific attributes
  - Category-based reporting and filtering
  - Asset template management

- **Assignment & Due Date Tracking**
  - Employee asset assignments with automatic due date calculation
  - Overdue asset notifications and alerts
  - Bulk assignment and reassignment capabilities
  - Return tracking and verification

- **Issue Management**
  - Asset problem tracking and resolution workflows
  - Issue assignment to technical staff
  - Issue priority and status management
  - Resolution tracking and reporting

- **Ticket Management**
  - Service desk ticket creation and management
  - Ticket queue organization and routing
  - SLA tracking and escalation procedures
  - Ticket type configuration and workflows

**Key Pages:**
- `/app/product.serviceops/` - Service operations dashboard
- `/app/product.serviceops/fixed-assets` - Asset management interface
- `/app/product.serviceops/fixed-assets/dashboard` - Asset analytics dashboard
- `/app/product.serviceops/fixed-assets/categories` - Asset category management
- `/app/product.serviceops/tickets` - Service desk interface
- `/app/product.serviceops/queues` - Ticket queue management

**UI Features:**
- Dashboard with key metrics cards and charts
- Asset lists with status badges and due date indicators
- Multi-tab forms for asset details (overview, finance, issues)
- Calendar integration for due dates and scheduling
- HTMX tables for dynamic asset listings

### 3. Timesheets - Time Tracking & Management

**Main Features:**
- **Personal Timesheet Management**
  - Daily time logging with project/task categorization
  - Timer functionality for active work tracking
  - Time entry editing and approval workflows
  - Personal productivity tracking

- **Timer Functionality**
  - Start/stop timers for active work sessions
  - Real-time timer display with notifications
  - Automatic time calculation and rounding
  - Break time tracking and management

- **Account & Work Type Tracking**
  - Categorized time entries by client/project
  - Work type classification for billing purposes
  - Rate management and billing calculations
  - Time allocation reporting

- **Progress Tracking**
  - Daily hour requirements vs. logged time
  - Weekly and monthly time summaries
  - Productivity analytics and trends
  - Goal setting and achievement tracking

**Key Pages:**
- `/app/product.timesheets/` - Personal timesheet interface
- `/app/product.timesheets/setup` - Timesheet configuration
- `/app/product.timesheets/accounts` - Account/project setup
- `/app/product.timesheets/staff` - Staff management
- `/app/product.timesheets/reports` - Time reporting and analytics

**UI Features:**
- Interactive timesheet with timer controls
- Date picker navigation (previous/next day)
- Progress bars showing daily completion
- Modal forms for time entry
- Real-time timer updates and progress tracking

### 4. AccountOps - Financial Management

**Main Features:**
- **Cashbook Management**
  - Bank account reconciliation and management
  - Transaction recording and categorization
  - Automated bank statement import
  - Cash flow tracking and reporting

- **Invoicing System**
  - Invoice creation and management
  - Customer billing and payment tracking
  - Recurring invoice automation
  - Invoice templates and customization

- **Expense Management**
  - Employee expense claim submission
  - Expense approval workflows
  - Receipt management and storage
  - Expense categorization and reporting

- **Budget Management**
  - Budget creation and allocation
  - Budget vs. actual reporting
  - Variance analysis and alerts
  - Multi-period budget planning

- **Financial Reporting**
  - Profit and loss statements
  - Balance sheet generation
  - Cash flow statements
  - Custom financial reports

**Key Pages:**
- `/app/product.accounts/` - Accounting dashboard
- `/app/product.accounts/cashbook` - Cash management
- `/app/product.accounts/invoices` - Invoice management
- `/app/product.accounts/expenses` - Expense tracking
- `/app/product.accounts/budgets` - Budget management
- `/app/product.accounts/reports` - Financial reporting

**UI Features:**
- Import functionality for bank statements
- Multi-step forms for complex financial data
- Report generation with print layouts
- Chart integration for financial visualizations
- Reconciliation interfaces with matching tools

### 5. System Settings - Administration

**Main Features:**
- **User Management**
  - User account creation and management
  - Role assignment and permission control
  - User group organization and management
  - Access control and security settings

- **Entity Configuration**
  - Organization settings and customization
  - Entity hierarchy management
  - Branch configuration and setup
  - Multi-tenant administration

- **License Management**
  - Product licensing and feature control
  - License allocation and tracking
  - Feature enablement and restrictions
  - License renewal and management

- **Workflow Configuration**
  - State transition management
  - Business process automation setup
  - Workflow step configuration
  - Approval process definition

- **Menu Customization**
  - Custom navigation setup
  - Menu item configuration
  - User-specific menu customization
  - Navigation permission control

**Key Pages:**
- `/app/product.systemsettings/` - System administration dashboard
- `/app/product.systemsettings/users` - User management
- `/app/product.systemsettings/groups` - Group management
- `/app/product.systemsettings/entities` - Entity configuration
- `/app/product.systemsettings/workflows` - Workflow management
- `/app/product.systemsettings/menus` - Menu customization

**UI Features:**
- Multi-tab configuration interfaces
- Workflow state diagrams and transition forms
- Permission matrices and role assignments
- Customizable header colors and branding
- Advanced search and filtering capabilities

## Supporting Systems

### 6. Dashboard - Main Overview

**Main Features:**
- **Product Access Hub**
  - Navigation to major application modules
  - Quick access cards for CustomerOps, Asset Manager, Timesheets
  - Feature introduction and onboarding

- **Notification System**
  - Real-time push notifications
  - System alerts and warnings
  - User activity notifications
  - Background job status updates

**Key Pages:**
- `/app/dashboard/` - Main application dashboard
- `/app/dashboard/notifications` - Notification center
- `/app/dashboard/about` - System information

### 7. Portal System - Customer Self-Service

**Main Features:**
- **Customer Portal**
  - External customer access interface
  - Self-service support request submission
  - Account information access
  - Document sharing and collaboration

- **Support System**
  - Ticket submission interface
  - Knowledge base browsing
  - Support topic organization
  - FAQ and help documentation

**Key Pages:**
- `/portal/` - Customer portal landing
- `/portal/support` - Support request system
- `/portal/knowledge` - Knowledge base
- `/portal/account` - Customer account management

### 8. File & Document Management

**Main Features:**
- **Document Storage**
  - File upload and download management
  - Document categorization and tagging
  - Version control and history tracking
  - Secure file sharing capabilities

- **External Sharing**
  - Secure document sharing with external parties
  - Time-limited access controls
  - Share tracking and analytics
  - Password-protected sharing

**Key Pages:**
- `/app/files/` - File management interface
- `/app/files/collections` - Document collections
- `/share/` - External sharing interface

### 9. WorkflowOps - Business Process Automation

**Main Features:**
- **Workflow Engine**
  - Custom business process automation
  - State transition management
  - Parameter-driven workflow execution
  - Conditional logic and branching

- **Process Management**
  - Workflow template creation
  - Process monitoring and tracking
  - Automated task assignment
  - Approval routing and notifications

**Key Pages:**
- `/app/product.workflowops/` - Workflow management
- `/app/product.workflowops/workflows` - Process configuration
- `/app/product.workflowops/monitoring` - Process monitoring

## Technical Features

### Advanced UI Capabilities

- Dynamic content loading without page refreshes
- Real-time list updates and live data
- Form submissions with partial page updates
- Timer functionality with live updates

**PrivacyOps Framework:**
- Advanced search and filter capabilities
- Customizable table views and column selection
- Export functions (PDF, Excel, CSV, Print)
- Saved search queries and quick filters
- Efficient pagination for large datasets

**Responsive Design:**
- Mobile-friendly navigation with collapsible sidebars
- Touch-friendly interface elements
- Responsive tables with horizontal scrolling
- Print-optimized layouts

### Data Management

**Form Capabilities:**
- Inline editing with click-to-edit functionality
- Client and server-side validation
- Auto-complete and searchable dropdowns
- File upload with drag-and-drop support
- Rich text editing with WYSIWYG editors

**State Management:**
- Visual state transitions with approval workflows
- Color-coded status indicators and badges
- Permission-based field access and editing
- Audit trails and change tracking

### Integration & APIs

**API Endpoints:**
- Comprehensive REST API coverage
- JSON response format with standardized structure
- Third-party integration capabilities
- Webhook support for external systems

**Background Processing:**
- Asynchronous job processing
- Scheduled task management
- Email queue processing
- Data import/export operations

## Security & Compliance

### Authentication & Authorization
- Multi-factor authentication with various methods
- Session management and timeout controls
- API key authentication for external access
- Role-based permission system

### Data Protection
- Field-level security controls
- Data encryption at rest and in transit
- Audit logging and compliance tracking
- Privacy controls and data masking

### Multi-Tenancy Security
- Entity-based data isolation
- Cross-tenant data protection
- Branch-level security controls
- HQ administrative oversight

## DOCUMENTATION:

For up-to-date documentation, always use the context7 MCP server
