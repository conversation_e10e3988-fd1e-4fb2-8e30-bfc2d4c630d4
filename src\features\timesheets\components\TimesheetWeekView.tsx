import { useState, useMemo } from 'react';
import { 
  Calendar, 
  Plus, 
  Edit, 
  Trash2, 
  Clock, 
  Send, 
  MoreHorizontal,
  Play,
  ChevronLeft,
  ChevronRight,
  Briefcase,
  DollarSign
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { 
  useTimesheets, 
  useTimeEntries, 
  useSubmitTimesheet, 
  useDeleteTimeEntry,
  useStartTimer
} from '../hooks/useTimesheets';
import { TimeEntry, TimesheetStatus } from '../schemas/timesheetSchemas';
import { SimpleTimeEntryForm } from './SimpleTimeEntryForm';

interface TimesheetWeekViewProps {
  userId: string;
  entityId: string;
  weekStartDate: string; // YYYY-MM-DD format
  onWeekChange?: (newWeekStart: string) => void;
}

interface DayColumn {
  date: string;
  dayName: string;
  dayNumber: number;
  entries: TimeEntry[];
  totalHours: number;
}

export function TimesheetWeekView({ 
  userId, 
  entityId, 
  weekStartDate, 
  onWeekChange 
}: TimesheetWeekViewProps) {
  const [selectedEntry, setSelectedEntry] = useState<TimeEntry | null>(null);
  const [showEntryForm, setShowEntryForm] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [entryToDelete, setEntryToDelete] = useState<TimeEntry | null>(null);

  const startDate = new Date(weekStartDate);
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);

  const { data: timesheetData } = useTimesheets({
    userId,
    weekStartDate,
    limit: 1,
    page: 1,
    sortBy: 'date',
    sortOrder: 'desc',
  });

  const { data: entriesData } = useTimeEntries({
    userId,
    dateFrom: weekStartDate,
    dateTo: endDate.toISOString().split('T')[0],
    limit: 100,
    page: 1,
    sortBy: 'date',
    sortOrder: 'desc',
  });

  const submitTimesheet = useSubmitTimesheet();
  const deleteEntry = useDeleteTimeEntry();
  const startTimer = useStartTimer();

  const timesheet = timesheetData?.timesheets[0];
  const entries = entriesData?.entries || [];

  // Generate week columns
  const weekColumns: DayColumn[] = useMemo(() => {
    const columns: DayColumn[] = [];
    const current = new Date(startDate);

    for (let i = 0; i < 7; i++) {
      const dateStr = current.toISOString().split('T')[0];
      const dayEntries = entries.filter(entry => entry.date === dateStr);
      const totalHours = dayEntries.reduce((sum, entry) => sum + entry.duration, 0);

      columns.push({
        date: dateStr,
        dayName: current.toLocaleDateString('en-US', { weekday: 'short' }),
        dayNumber: current.getDate(),
        entries: dayEntries,
        totalHours,
      });

      current.setDate(current.getDate() + 1);
    }

    return columns;
  }, [startDate, entries]);

  const weekTotals = useMemo(() => {
    const totalHours = entries.reduce((sum, entry) => sum + entry.duration, 0);
    const billableHours = entries.filter(e => e.billable).reduce((sum, entry) => sum + entry.duration, 0);
    const totalAmount = entries.reduce((sum, entry) => sum + (entry.totalAmount || 0), 0);

    return { totalHours, billableHours, totalAmount };
  }, [entries]);

  const handlePreviousWeek = () => {
    const prevWeek = new Date(startDate);
    prevWeek.setDate(prevWeek.getDate() - 7);
    onWeekChange?.(prevWeek.toISOString().split('T')[0]);
  };

  const handleNextWeek = () => {
    const nextWeek = new Date(startDate);
    nextWeek.setDate(nextWeek.getDate() + 7);
    onWeekChange?.(nextWeek.toISOString().split('T')[0]);
  };

  const handleAddEntry = () => {
    setSelectedEntry(null);
    setShowEntryForm(true);
  };

  const handleEditEntry = (entry: TimeEntry) => {
    setSelectedEntry(entry);
    setShowEntryForm(true);
  };

  const handleDeleteEntry = async () => {
    if (!entryToDelete) return;

    try {
      await deleteEntry.mutateAsync(entryToDelete.id);
      setDeleteDialogOpen(false);
      setEntryToDelete(null);
    } catch (error) {
      console.error('Failed to delete entry:', error);
    }
  };

  const handleStartTimer = async (entry: TimeEntry) => {
    try {
      await startTimer.mutateAsync({
        description: entry.description,
        projectId: entry.projectId,
        taskId: entry.taskId,
        userId,
        entityId,
      });
    } catch (error) {
      console.error('Failed to start timer:', error);
    }
  };

  const handleSubmitTimesheet = async () => {
    if (!timesheet) return;

    try {
      await submitTimesheet.mutateAsync(timesheet.id);
    } catch (error) {
      console.error('Failed to submit timesheet:', error);
    }
  };

  const getStatusColor = (status: TimesheetStatus) => {
    const colors = {
      draft: 'bg-gray-100 text-gray-800',
      submitted: 'bg-blue-100 text-blue-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      pending_review: 'bg-yellow-100 text-yellow-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatHours = (hours: number) => {
    return hours.toFixed(2);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const isEditable = !timesheet || timesheet.status === 'draft' || timesheet.status === 'rejected';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Weekly Timesheet</h2>
          <div className="flex items-center gap-4 mt-2">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handlePreviousWeek}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-lg font-medium">
                {startDate.toLocaleDateString('en-US', { 
                  month: 'long', 
                  day: 'numeric', 
                  year: 'numeric' 
                })} - {endDate.toLocaleDateString('en-US', { 
                  month: 'long', 
                  day: 'numeric', 
                  year: 'numeric' 
                })}
              </span>
              <Button variant="outline" size="sm" onClick={handleNextWeek}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            {timesheet && (
              <Badge variant="outline" className={getStatusColor(timesheet.status)}>
                {timesheet.status.replace('_', ' ')}
              </Badge>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          {isEditable && (
            <RoleGuard permissions={[PERMISSIONS['timesheets:write']]}>
              <Button 
                variant="outline"
                onClick={handleSubmitTimesheet}
                disabled={!timesheet || timesheet.status !== 'draft' || weekTotals.totalHours === 0}
              >
                <Send className="mr-2 h-4 w-4" />
                Submit Week
              </Button>
            </RoleGuard>
          )}
        </div>
      </div>

      {/* Week Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Total Hours
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatHours(weekTotals.totalHours)}</div>
            <p className="text-xs text-muted-foreground">
              {formatHours(weekTotals.billableHours)} billable
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Total Amount
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(weekTotals.totalAmount)}</div>
            <p className="text-xs text-muted-foreground">From billable hours</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Days Logged
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {weekColumns.filter(day => day.totalHours > 0).length}/7
            </div>
            <p className="text-xs text-muted-foreground">Days with time entries</p>
          </CardContent>
        </Card>
      </div>

      {/* Week Grid */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                {weekColumns.map((day) => (
                  <TableHead key={day.date} className="text-center min-w-32">
                    <div className="space-y-1">
                      <div className="font-medium">{day.dayName}</div>
                      <div className="text-2xl font-bold">{day.dayNumber}</div>
                      <div className="text-xs text-muted-foreground">
                        {formatHours(day.totalHours)}h
                      </div>
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                {weekColumns.map((day) => (
                  <TableCell key={day.date} className="align-top p-2">
                    <div className="space-y-2 min-h-48">
                      {day.entries.map((entry) => (
                        <Card key={entry.id} className="p-2 hover:shadow-sm transition-shadow">
                          <div className="space-y-1">
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <p className="text-xs font-medium truncate">
                                  {entry.description}
                                </p>
                                {entry.projectName && (
                                  <div className="flex items-center gap-1 mt-1">
                                    <Briefcase className="h-3 w-3 text-muted-foreground" />
                                    <span className="text-xs text-muted-foreground truncate">
                                      {entry.projectName}
                                    </span>
                                  </div>
                                )}
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                    <MoreHorizontal className="h-3 w-3" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => handleStartTimer(entry)}>
                                    <Play className="mr-2 h-3 w-3" />
                                    Start Timer
                                  </DropdownMenuItem>
                                  {isEditable && (
                                    <>
                                      <RoleGuard permissions={[PERMISSIONS['timesheets:write']]}>
                                        <DropdownMenuItem onClick={() => handleEditEntry(entry)}>
                                          <Edit className="mr-2 h-3 w-3" />
                                          Edit
                                        </DropdownMenuItem>
                                      </RoleGuard>
                                      <RoleGuard permissions={[PERMISSIONS['timesheets:delete']]}>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                          className="text-red-600"
                                          onClick={() => {
                                            setEntryToDelete(entry);
                                            setDeleteDialogOpen(true);
                                          }}
                                        >
                                          <Trash2 className="mr-2 h-3 w-3" />
                                          Delete
                                        </DropdownMenuItem>
                                      </RoleGuard>
                                    </>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                            
                            <div className="flex items-center justify-between text-xs">
                              <span className="font-medium">
                                {formatHours(entry.duration)}h
                              </span>
                              <div className="flex items-center gap-1">
                                <Badge 
                                  variant="outline" 
                                  className={`text-xs ${entry.billable ? 'bg-green-50 text-green-700' : 'bg-gray-50 text-gray-700'}`}
                                >
                                  {entry.billable ? 'Billable' : 'Non-billable'}
                                </Badge>
                              </div>
                            </div>
                            
                            {entry.startTime && entry.endTime && (
                              <div className="text-xs text-muted-foreground">
                                {entry.startTime} - {entry.endTime}
                              </div>
                            )}
                          </div>
                        </Card>
                      ))}
                      
                      {isEditable && (
                        <RoleGuard permissions={[PERMISSIONS['timesheets:write']]}>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full h-8 text-xs"
                            onClick={() => handleAddEntry()}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Add Entry
                          </Button>
                        </RoleGuard>
                      )}
                    </div>
                  </TableCell>
                ))}
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Time Entry Form Dialog */}
      {showEntryForm && (
        <Dialog open={showEntryForm} onOpenChange={setShowEntryForm}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <SimpleTimeEntryForm
              entityId={entityId}
              userId={userId}
              userName={timesheet?.userName || ''}
              userEmail={timesheet?.userEmail || ''}
              entry={selectedEntry || undefined}
              onSubmit={() => setShowEntryForm(false)}
              onCancel={() => setShowEntryForm(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Time Entry</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this time entry? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteEntry}
              disabled={deleteEntry.isPending}
            >
              {deleteEntry.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}