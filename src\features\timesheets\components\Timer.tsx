import { useState, useEffect } from 'react';
import { 
  Play, 
  Pause, 
  Square, 
  Clock, 
  ChevronDown,
  Briefcase,
  CheckSquare,
  Edit,
  Save,
  X
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { 
  useActiveTimerSession,
  useStartTimer,
  usePauseTimer,
  useResumeTimer,
  useStopTimer,
  useProjects,
  useTasks
} from '../hooks/useTimesheets';
// Timer component types are handled inline

interface TimerProps {
  userId: string;
  entityId: string;
  onTimeEntryCreated?: () => void;
}

export function Timer({ userId, entityId, onTimeEntryCreated }: TimerProps) {
  const [description, setDescription] = useState('');
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [selectedTaskId, setSelectedTaskId] = useState<string>('');
  const [editMode, setEditMode] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentTime, setCurrentTime] = useState<string>('00:00:00');

  const { data: activeSession } = useActiveTimerSession(userId);
  const { data: projects } = useProjects(entityId);
  const { data: tasks } = useTasks(selectedProjectId);
  
  const startTimer = useStartTimer();
  const pauseTimer = usePauseTimer();
  const resumeTimer = useResumeTimer();
  const stopTimer = useStopTimer();

  const selectedProject = projects?.find(p => p.id === selectedProjectId);
  const selectedTask = tasks?.find(t => t.id === selectedTaskId);

  // Calculate elapsed time
  useEffect(() => {
    if (!activeSession || activeSession.status === 'stopped') {
      setCurrentTime('00:00:00');
      return;
    }

    const updateTime = () => {
      const startTime = new Date(activeSession.startTime);
      const now = new Date();
      const pausedTime = activeSession.pausedTime || 0;
      
      let elapsedMs = now.getTime() - startTime.getTime() - (pausedTime * 1000);
      
      // If paused, subtract time since last pause
      if (activeSession.status === 'paused' && activeSession.pauseHistory.length > 0) {
        const lastPause = activeSession.pauseHistory[activeSession.pauseHistory.length - 1];
        if (lastPause.pausedAt && !lastPause.resumedAt) {
          const pauseStart = new Date(lastPause.pausedAt);
          elapsedMs -= (now.getTime() - pauseStart.getTime());
        }
      }

      const elapsedSeconds = Math.max(0, Math.floor(elapsedMs / 1000));
      const hours = Math.floor(elapsedSeconds / 3600);
      const minutes = Math.floor((elapsedSeconds % 3600) / 60);
      const seconds = elapsedSeconds % 60;

      setCurrentTime(
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      );
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, [activeSession]);

  // Sync form data with active session
  useEffect(() => {
    if (activeSession) {
      setDescription(activeSession.description);
      setSelectedProjectId(activeSession.projectId || '');
      setSelectedTaskId(activeSession.taskId || '');
      setIsExpanded(true);
    }
  }, [activeSession]);

  const handleStart = async () => {
    if (!description.trim()) return;

    try {
      await startTimer.mutateAsync({
        description: description.trim(),
        projectId: selectedProjectId || undefined,
        taskId: selectedTaskId || undefined,
        userId,
        entityId,
      });
      setEditMode(false);
    } catch (error) {
      console.error('Failed to start timer:', error);
    }
  };

  const handlePause = async () => {
    if (!activeSession) return;

    try {
      await pauseTimer.mutateAsync(activeSession.id);
    } catch (error) {
      console.error('Failed to pause timer:', error);
    }
  };

  const handleResume = async () => {
    if (!activeSession) return;

    try {
      await resumeTimer.mutateAsync(activeSession.id);
    } catch (error) {
      console.error('Failed to resume timer:', error);
    }
  };

  const handleStop = async () => {
    if (!activeSession) return;

    try {
      await stopTimer.mutateAsync(activeSession.id);
      setDescription('');
      setSelectedProjectId('');
      setSelectedTaskId('');
      setEditMode(false);
      setIsExpanded(false);
      onTimeEntryCreated?.();
    } catch (error) {
      console.error('Failed to stop timer:', error);
    }
  };

  const handleUpdateSession = async () => {
    // This would require an update timer session endpoint
    // For now, we just toggle edit mode
    setEditMode(false);
  };

  const isRunning = activeSession?.status === 'running';
  const isStopped = !activeSession || activeSession.status === 'stopped';

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Time Tracker
          </CardTitle>
          {!isStopped && (
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  <ChevronDown className={`h-4 w-4 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Timer Display */}
        <div className="text-center">
          <div className="text-4xl font-mono font-bold text-primary mb-2">
            {currentTime}
          </div>
          <div className="flex justify-center gap-2">
            {isStopped ? (
              <RoleGuard permissions={[PERMISSIONS['timesheets:write']]}>
                <Button 
                  onClick={handleStart}
                  disabled={!description.trim() || startTimer.isPending}
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  Start Timer
                </Button>
              </RoleGuard>
            ) : (
              <div className="flex gap-2">
                <RoleGuard permissions={[PERMISSIONS['timesheets:write']]}>
                  {isRunning ? (
                    <Button 
                      variant="outline"
                      onClick={handlePause}
                      disabled={pauseTimer.isPending}
                      className="flex items-center gap-2"
                    >
                      <Pause className="h-4 w-4" />
                      Pause
                    </Button>
                  ) : (
                    <Button 
                      onClick={handleResume}
                      disabled={resumeTimer.isPending}
                      className="flex items-center gap-2"
                    >
                      <Play className="h-4 w-4" />
                      Resume
                    </Button>
                  )}
                  <Button 
                    variant="destructive"
                    onClick={handleStop}
                    disabled={stopTimer.isPending}
                    className="flex items-center gap-2"
                  >
                    <Square className="h-4 w-4" />
                    Stop
                  </Button>
                </RoleGuard>
              </div>
            )}
          </div>
        </div>

        {/* Timer Form */}
        <Collapsible open={isExpanded || isStopped} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-4">
            {/* Current Activity Summary (when timer is running) */}
            {!isStopped && !editMode && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Current Activity</Label>
                  <RoleGuard permissions={[PERMISSIONS['timesheets:write']]}>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setEditMode(true)}
                      className="h-6 text-xs"
                    >
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  </RoleGuard>
                </div>
                <div className="p-3 bg-muted rounded-lg">
                  <p className="font-medium">{description}</p>
                  {selectedProject && (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                      <Briefcase className="h-3 w-3" />
                      <span>{selectedProject.name}</span>
                      {selectedTask && (
                        <>
                          <span>/</span>
                          <CheckSquare className="h-3 w-3" />
                          <span>{selectedTask.name}</span>
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Edit Form */}
            {(isStopped || editMode) && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Input
                    id="description"
                    placeholder="What are you working on?"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full"
                    maxLength={200}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="project">Project</Label>
                    <Select 
                      value={selectedProjectId} 
                      onValueChange={(value) => {
                        setSelectedProjectId(value);
                        setSelectedTaskId(''); // Reset task when project changes
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select project" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No Project</SelectItem>
                        {projects?.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            <div className="flex items-center gap-2">
                              <Briefcase className="h-4 w-4" />
                              <span>{project.name}</span>
                              {project.code && (
                                <span className="text-xs text-muted-foreground">
                                  ({project.code})
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="task">Task</Label>
                    <Select 
                      value={selectedTaskId} 
                      onValueChange={setSelectedTaskId}
                      disabled={!selectedProjectId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select task" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No Task</SelectItem>
                        {tasks?.map((task) => (
                          <SelectItem key={task.id} value={task.id}>
                            <div className="flex items-center gap-2">
                              <CheckSquare className="h-4 w-4" />
                              <span>{task.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Edit mode buttons */}
                {editMode && !isStopped && (
                  <div className="flex justify-end gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setEditMode(false)}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Cancel
                    </Button>
                    <Button 
                      size="sm"
                      onClick={handleUpdateSession}
                    >
                      <Save className="h-4 w-4 mr-1" />
                      Update
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Quick Actions */}
            {isStopped && (
              <div className="grid grid-cols-2 gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setDescription('Development work')}
                  className="text-xs"
                >
                  Development
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setDescription('Meeting')}
                  className="text-xs"
                >
                  Meeting
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setDescription('Code review')}
                  className="text-xs"
                >
                  Code Review
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setDescription('Planning')}
                  className="text-xs"
                >
                  Planning
                </Button>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Status indicator */}
        {!isStopped && (
          <div className="flex items-center justify-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${
              isRunning ? 'bg-green-500 animate-pulse' : 'bg-yellow-500'
            }`} />
            <span className="text-muted-foreground">
              {isRunning ? 'Timer is running' : 'Timer is paused'}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}