import { z } from "zod";

// Timesheet Entry Status Enum
export const timesheetStatusSchema = z.enum([
  'draft',
  'submitted',
  'approved',
  'rejected',
  'pending_review'
], {
  required_error: "Timesheet status is required",
});

export type TimesheetStatus = z.infer<typeof timesheetStatusSchema>;

// Time Entry Type Enum
export const timeEntryTypeSchema = z.enum([
  'regular',
  'overtime',
  'break',
  'training',
  'meeting',
  'travel',
  'sick',
  'vacation',
  'personal',
  'holiday'
], {
  required_error: "Time entry type is required",
});

export type TimeEntryType = z.infer<typeof timeEntryTypeSchema>;

// Approval Status Enum
export const approvalStatusSchema = z.enum([
  'pending',
  'approved',
  'rejected',
  'needs_revision'
], {
  required_error: "Approval status is required",
});

export type ApprovalStatus = z.infer<typeof approvalStatusSchema>;

// Timer Status Enum
export const timerStatusSchema = z.enum([
  'stopped',
  'running',
  'paused'
], {
  required_error: "Timer status is required",
});

export type TimerStatus = z.infer<typeof timerStatusSchema>;

// Project Reference Schema
export const projectReferenceSchema = z.object({
  id: z.string(),
  name: z.string(),
  code: z.string().optional(),
  clientId: z.string().optional(),
  clientName: z.string().optional(),
  hourlyRate: z.number().min(0).optional(),
  budget: z.number().min(0).optional(),
  isActive: z.boolean().default(true),
});

export type ProjectReference = z.infer<typeof projectReferenceSchema>;

// Task Reference Schema
export const taskReferenceSchema = z.object({
  id: z.string(),
  name: z.string(),
  projectId: z.string(),
  description: z.string().optional(),
  estimatedHours: z.number().min(0).optional(),
  hourlyRate: z.number().min(0).optional(),
  isActive: z.boolean().default(true),
});

export type TaskReference = z.infer<typeof taskReferenceSchema>;

// Time Entry Schema
export const timeEntrySchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // User Information
  userId: z.string(),
  userName: z.string(),
  userEmail: z.string().email(),
  
  // Time Details
  date: z.string(),
  startTime: z.string(),
  endTime: z.string().optional(),
  duration: z.number().min(0), // in hours
  
  // Entry Details
  type: timeEntryTypeSchema,
  description: z.string().min(1, "Description is required"),
  notes: z.string().optional(),
  
  // Project/Task Association
  projectId: z.string().optional(),
  projectName: z.string().optional(),
  taskId: z.string().optional(),
  taskName: z.string().optional(),
  
  // Billing Information
  billable: z.boolean().default(true),
  hourlyRate: z.number().min(0).optional(),
  totalAmount: z.number().min(0).optional(),
  
  // Status
  status: timesheetStatusSchema,
  
  // Timer Information
  timerStatus: timerStatusSchema.default('stopped'),
  timerStartedAt: z.string().optional(),
  timerPausedAt: z.string().optional(),
  timerTotalPaused: z.number().default(0), // in minutes
  
  // Approval Information
  approvalStatus: approvalStatusSchema.default('pending'),
  approvedByUserId: z.string().optional(),
  approvedByUserName: z.string().optional(),
  approvedAt: z.string().optional(),
  rejectionReason: z.string().optional(),
  
  // Attachments
  attachments: z.array(z.object({
    id: z.string(),
    name: z.string(),
    url: z.string(),
    size: z.number(),
    mimeType: z.string(),
  })).default([]),
  
  // Tags and Categories
  tags: z.array(z.string()).default([]),
  category: z.string().optional(),
  
  // External References
  ticketId: z.string().optional(),
  ticketNumber: z.string().optional(),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
  submittedAt: z.string().optional(),
  
  // Location (for mobile tracking)
  location: z.object({
    latitude: z.number().optional(),
    longitude: z.number().optional(),
    address: z.string().optional(),
  }).optional(),
});

export type TimeEntry = z.infer<typeof timeEntrySchema>;

// Timesheet Schema (collection of entries)
export const timesheetSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // User Information
  userId: z.string(),
  userName: z.string(),
  userEmail: z.string().email(),
  
  // Period Information
  weekStartDate: z.string(),
  weekEndDate: z.string(),
  year: z.number(),
  weekNumber: z.number(),
  
  // Entries
  entries: z.array(timeEntrySchema).default([]),
  
  // Totals
  totalHours: z.number().min(0),
  totalBillableHours: z.number().min(0),
  totalAmount: z.number().min(0),
  
  // Status and Approval
  status: timesheetStatusSchema,
  approvalStatus: approvalStatusSchema.default('pending'),
  approvedByUserId: z.string().optional(),
  approvedByUserName: z.string().optional(),
  approvedAt: z.string().optional(),
  rejectionReason: z.string().optional(),
  
  // Submission
  submittedAt: z.string().optional(),
  submittedByUserId: z.string().optional(),
  submittedByUserName: z.string().optional(),
  
  // Comments
  comments: z.array(z.object({
    id: z.string(),
    content: z.string(),
    authorId: z.string(),
    authorName: z.string(),
    createdAt: z.string(),
    isInternal: z.boolean().default(false),
  })).default([]),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type Timesheet = z.infer<typeof timesheetSchema>;

// Timer Session Schema
export const timerSessionSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // User Information
  userId: z.string(),
  userName: z.string(),
  
  // Timer Details
  status: timerStatusSchema,
  startTime: z.string(),
  endTime: z.string().optional(),
  pausedTime: z.number().default(0), // total paused time in seconds
  
  // Entry Details
  description: z.string().min(1, "Description is required"),
  projectId: z.string().optional(),
  projectName: z.string().optional(),
  taskId: z.string().optional(),
  taskName: z.string().optional(),
  
  // Current session tracking
  currentSessionStart: z.string().optional(),
  pauseHistory: z.array(z.object({
    pausedAt: z.string(),
    resumedAt: z.string().optional(),
  })).default([]),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type TimerSession = z.infer<typeof timerSessionSchema>;

// Approval Workflow Schema
export const approvalWorkflowSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // Workflow Details
  name: z.string().min(1, "Workflow name is required"),
  description: z.string().optional(),
  
  // Rules
  isActive: z.boolean().default(true),
  autoApprovalThreshold: z.number().min(0).optional(), // hours
  requiresApprovalAbove: z.number().min(0).optional(), // hours
  
  // Approvers
  approvers: z.array(z.object({
    userId: z.string(),
    userName: z.string(),
    email: z.string().email(),
    level: z.number().min(1), // approval level (1 = primary, 2 = secondary, etc.)
    canApproveAll: z.boolean().default(false),
    maxApprovalAmount: z.number().min(0).optional(),
  })),
  
  // Notification Settings
  notifications: z.object({
    submitNotification: z.boolean().default(true),
    approvalNotification: z.boolean().default(true),
    rejectionNotification: z.boolean().default(true),
    reminderEnabled: z.boolean().default(true),
    reminderDays: z.number().min(1).default(2),
  }),
  
  // Business Rules
  businessRules: z.object({
    maxHoursPerDay: z.number().min(1).default(24),
    maxHoursPerWeek: z.number().min(1).default(168),
    requireProjectForBillable: z.boolean().default(true),
    allowOvertime: z.boolean().default(true),
    overtimeThreshold: z.number().min(0).default(40),
    roundingRules: z.object({
      enabled: z.boolean().default(false),
      roundTo: z.enum(['5', '10', '15', '30']).default('15'), // minutes
      roundingMethod: z.enum(['up', 'down', 'nearest']).default('nearest'),
    }),
  }),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type ApprovalWorkflow = z.infer<typeof approvalWorkflowSchema>;

// Form Schemas
export const timeEntryFormSchema = timeEntrySchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  submittedAt: true,
  approvalStatus: true,
  approvedByUserId: true,
  approvedByUserName: true,
  approvedAt: true,
  timerStatus: true,
  timerStartedAt: true,
  timerPausedAt: true,
  timerTotalPaused: true,
}).extend({
  entityId: z.string().min(1, "Entity is required"),
  userId: z.string().min(1, "User is required"),
  userName: z.string().min(1, "User name is required"),
  userEmail: z.string().email("Valid email is required"),
});

export type TimeEntryFormValues = z.infer<typeof timeEntryFormSchema>;

export const timesheetFormSchema = timesheetSchema.omit({
  id: true,
  entries: true,
  totalHours: true,
  totalBillableHours: true,
  totalAmount: true,
  approvalStatus: true,
  approvedByUserId: true,
  approvedByUserName: true,
  approvedAt: true,
  submittedAt: true,
  submittedByUserId: true,
  submittedByUserName: true,
  comments: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  entityId: z.string().min(1, "Entity is required"),
  userId: z.string().min(1, "User is required"),
  userName: z.string().min(1, "User name is required"),
  userEmail: z.string().email("Valid email is required"),
});

export type TimesheetFormValues = z.infer<typeof timesheetFormSchema>;

// Search and Filter Schemas
export const timesheetSearchSchema = z.object({
  query: z.string().optional(),
  userId: z.string().optional(),
  status: timesheetStatusSchema.optional(),
  approvalStatus: approvalStatusSchema.optional(),
  projectId: z.string().optional(),
  taskId: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  weekStartDate: z.string().optional(),
  billable: z.boolean().optional(),
  type: timeEntryTypeSchema.optional(),
  sortBy: z.enum(['date', 'duration', 'userName', 'projectName', 'status', 'createdAt', 'updatedAt']).optional().default('date'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(20),
});

export type TimesheetSearchParams = z.infer<typeof timesheetSearchSchema>;

// Response Schemas
export const timesheetListResponseSchema = z.object({
  timesheets: z.array(timesheetSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

export type TimesheetListResponse = z.infer<typeof timesheetListResponseSchema>;

export const timeEntryListResponseSchema = z.object({
  entries: z.array(timeEntrySchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

export type TimeEntryListResponse = z.infer<typeof timeEntryListResponseSchema>;

// Statistics Schema
export const timesheetStatsSchema = z.object({
  totalHours: z.number(),
  totalBillableHours: z.number(),
  totalAmount: z.number(),
  averageHoursPerDay: z.number(),
  averageHoursPerWeek: z.number(),
  
  statusBreakdown: z.array(z.object({
    status: timesheetStatusSchema,
    count: z.number(),
    percentage: z.number(),
  })),
  
  typeBreakdown: z.array(z.object({
    type: timeEntryTypeSchema,
    hours: z.number(),
    percentage: z.number(),
  })),
  
  projectBreakdown: z.array(z.object({
    projectId: z.string(),
    projectName: z.string(),
    hours: z.number(),
    amount: z.number(),
    percentage: z.number(),
  })),
  
  userBreakdown: z.array(z.object({
    userId: z.string(),
    userName: z.string(),
    totalHours: z.number(),
    billableHours: z.number(),
    amount: z.number(),
    averageHoursPerDay: z.number(),
  })),
  
  trendsLast30Days: z.array(z.object({
    date: z.string(),
    totalHours: z.number(),
    billableHours: z.number(),
    amount: z.number(),
  })),
  
  approvalStats: z.object({
    pending: z.number(),
    approved: z.number(),
    rejected: z.number(),
    averageApprovalTime: z.number(), // in hours
  }),
});

export type TimesheetStats = z.infer<typeof timesheetStatsSchema>;