import { useState } from 'react';
import { Save, Send, Eye } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { Quote, QuoteStatus, Deal } from '../schemas/dealSchemas';

interface QuoteBuilderProps {
  deal: Deal;
  quote?: Quote;
  onSave?: (quote: Quote) => void;
  onCancel?: () => void;
}

export function QuoteBuilder({ deal, quote, onSave, onCancel }: QuoteBuilderProps) {
  const [title, setTitle] = useState(quote?.title || `Quote for ${deal.title}`);
  const [description, setDescription] = useState(quote?.description || '');

  const getStatusColor = (status: QuoteStatus) => {
    const colors = {
      draft: 'bg-gray-100 text-gray-800',
      pending_approval: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      sent: 'bg-blue-100 text-blue-800',
      accepted: 'bg-emerald-100 text-emerald-800',
      rejected: 'bg-red-100 text-red-800',
      expired: 'bg-orange-100 text-orange-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const handleSave = () => {
    // Placeholder - would normally create/update quote
    console.log('Save quote:', { title, description });
    onSave?.({
      id: quote?.id || 'new-quote',
      dealId: deal.id,
      entityId: deal.entityId,
      title,
      description,
      status: 'draft' as QuoteStatus,
      quoteNumber: quote?.quoteNumber || 'Q-001',
      customerName: deal.customerName,
      customerId: deal.customerId,
      customerEmail: '',
      subtotal: 0,
      taxRate: 0,
      taxAmount: 0,
      discountAmount: 0,
      totalAmount: 0,
      terms: '',
      validUntil: '',
      paymentTerms: 'Net 30',
      lineItems: [],
      approvalRequired: false,
      createdBy: 'user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {quote ? 'Edit Quote' : 'Create Quote'}
          </h2>
          <p className="text-muted-foreground">
            Deal: {deal.title} - {deal.customerName}
          </p>
        </div>
        <div className="flex gap-2">
          {quote && (
            <>
              <Badge variant="outline" className={getStatusColor(quote.status)}>
                {quote.status.replace('_', ' ')}
              </Badge>
              <Button variant="outline" size="sm">
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </Button>
            </>
          )}
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </div>

      {/* Quote Form */}
      <Card>
        <CardHeader>
          <CardTitle>Quote Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Quote title"
            />
          </div>
          
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Quote description"
              rows={4}
            />
          </div>

          <div className="text-center py-8 text-muted-foreground">
            <p className="text-lg">Quote Builder Coming Soon</p>
            <p className="text-sm">
              This will include line items, pricing, terms & conditions, and approval workflows.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-between">
        <div className="flex gap-2">
          {quote && quote.status === 'draft' && (
            <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
              <Button variant="outline">
                <Send className="mr-2 h-4 w-4" />
                Send Quote
              </Button>
            </RoleGuard>
          )}
        </div>
        
        <div className="flex gap-2">
          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Quote
          </Button>
        </div>
      </div>
    </div>
  );
}