import { useState } from 'react';
import { 
  LayoutGrid, 
  List, 
  Plus, 
  TrendingUp 
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { useDealPipelineStats } from '../hooks/useDeals';
import { Deal } from '../schemas/dealSchemas';

import { DealPipeline } from './DealPipeline';
import { DealList } from './DealList';
import { QuoteBuilder } from './QuoteBuilder';

export function DealManagement() {
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [showQuoteBuilder, setShowQuoteBuilder] = useState(false);
  const [showDealForm, setShowDealForm] = useState(false);
  const [viewMode, setViewMode] = useState<'pipeline' | 'list'>('pipeline');

  const { data: pipelineStats } = useDealPipelineStats();

  const handleCreateDeal = () => {
    setSelectedDeal(null);
    setShowDealForm(true);
  };

  const handleEditDeal = (deal: Deal) => {
    setSelectedDeal(deal);
    setShowDealForm(true);
  };

  const handleViewDeal = (deal: Deal) => {
    setSelectedDeal(deal);
    // Could implement a detailed view dialog here
  };

  const handleCreateQuote = (deal: Deal) => {
    setSelectedDeal(deal);
    setShowQuoteBuilder(true);
  };

  const handleQuoteSaved = () => {
    setShowQuoteBuilder(false);
    setSelectedDeal(null);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Deal & Quote Management</h1>
          <p className="text-lg text-muted-foreground">
            Manage your sales pipeline and create quotes
          </p>
        </div>
        <div className="flex gap-2">
          <div className="flex rounded-lg border bg-background p-1">
            <Button
              variant={viewMode === 'pipeline' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('pipeline')}
            >
              <LayoutGrid className="mr-2 h-4 w-4" />
              Pipeline
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="mr-2 h-4 w-4" />
              List
            </Button>
          </div>
          <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
            <Button onClick={handleCreateDeal}>
              <Plus className="mr-2 h-4 w-4" />
              Create Deal
            </Button>
          </RoleGuard>
        </div>
      </div>

      {/* Pipeline Overview Stats */}
      {pipelineStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Total Deals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pipelineStats.totalDeals}</div>
              <p className="text-xs text-muted-foreground">Active opportunities</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pipeline Value</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(pipelineStats.totalValue)}</div>
              <p className="text-xs text-muted-foreground">Total potential revenue</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Average Deal Size</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(pipelineStats.averageDealSize)}</div>
              <p className="text-xs text-muted-foreground">Per opportunity</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pipelineStats.conversionRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                {pipelineStats.averageTimeToClose} days avg. close time
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'pipeline' | 'list')}>
        <TabsList className="hidden">
          <TabsTrigger value="pipeline">Pipeline</TabsTrigger>
          <TabsTrigger value="list">List</TabsTrigger>
        </TabsList>

        <TabsContent value="pipeline" className="space-y-4">
          <DealPipeline
            onCreateDeal={handleCreateDeal}
            onEditDeal={handleEditDeal}
            onViewDeal={handleViewDeal}
            onCreateQuote={handleCreateQuote}
          />
        </TabsContent>

        <TabsContent value="list" className="space-y-4">
          <DealList
            onCreateDeal={handleCreateDeal}
            onEditDeal={handleEditDeal}
            onViewDeal={handleViewDeal}
            onCreateQuote={handleCreateQuote}
          />
        </TabsContent>
      </Tabs>

      {/* Quote Builder Dialog */}
      <Dialog open={showQuoteBuilder} onOpenChange={setShowQuoteBuilder}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create Quote</DialogTitle>
          </DialogHeader>
          {selectedDeal && (
            <QuoteBuilder
              deal={selectedDeal}
              onSave={handleQuoteSaved}
              onCancel={() => setShowQuoteBuilder(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Deal Form Dialog - Placeholder for now */}
      {showDealForm && (
        <Dialog open={showDealForm} onOpenChange={setShowDealForm}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {selectedDeal ? 'Edit Deal' : 'Create Deal'}
              </DialogTitle>
            </DialogHeader>
            <div className="p-4">
              <p className="text-muted-foreground">
                Deal form component would go here. This would include fields for:
                title, customer, value, probability, status, priority, etc.
              </p>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setShowDealForm(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowDealForm(false)}>
                  Save Deal
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}