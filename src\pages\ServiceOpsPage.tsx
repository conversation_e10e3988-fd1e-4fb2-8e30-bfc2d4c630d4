import { 
  Package, 
  Ticket, 
  Wrench, 
  Users,
  TrendingUp
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';

export function ServiceOpsPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-4xl font-bold tracking-tight">ServiceOps Dashboard</h1>
        <p className="text-lg text-muted-foreground">
          Service operations management and asset tracking overview
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Package className="h-4 w-4" />
              Total Assets
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2,847</div>
            <p className="text-xs text-muted-foreground">+5% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Ticket className="h-4 w-4" />
              Open Tickets
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-xs text-muted-foreground">-12% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Wrench className="h-4 w-4" />
              Assets in Maintenance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">43</div>
            <p className="text-xs text-muted-foreground">15 scheduled this week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Asset Utilization
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87.3%</div>
            <p className="text-xs text-muted-foreground">+3.2% from last month</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="assets">Asset Health</TabsTrigger>
          <TabsTrigger value="tickets">Service Desk</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Asset LT-001 assigned to John Doe</p>
                      <p className="text-xs text-muted-foreground">30 minutes ago</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Maintenance completed on PR-045</p>
                      <p className="text-xs text-muted-foreground">2 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Ticket #1234 escalated to L2</p>
                      <p className="text-xs text-muted-foreground">4 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">New asset CP-789 added to inventory</p>
                      <p className="text-xs text-muted-foreground">6 hours ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <RoleGuard permissions={[PERMISSIONS['serviceops:write']]}>
                    <div className="p-4 border rounded-lg cursor-pointer hover:bg-muted/50">
                      <Package className="h-8 w-8 mb-2 text-blue-600" />
                      <p className="font-medium">Add Asset</p>
                    </div>
                    <div className="p-4 border rounded-lg cursor-pointer hover:bg-muted/50">
                      <Ticket className="h-8 w-8 mb-2 text-green-600" />
                      <p className="font-medium">Create Ticket</p>
                    </div>
                    <div className="p-4 border rounded-lg cursor-pointer hover:bg-muted/50">
                      <Wrench className="h-8 w-8 mb-2 text-purple-600" />
                      <p className="font-medium">Schedule Maintenance</p>
                    </div>
                    <div className="p-4 border rounded-lg cursor-pointer hover:bg-muted/50">
                      <Users className="h-8 w-8 mb-2 text-orange-600" />
                      <p className="font-medium">Assign Asset</p>
                    </div>
                  </RoleGuard>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="assets" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Asset Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Active</span>
                    <span className="text-sm font-medium text-green-600">2,456 (86%)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Maintenance</span>
                    <span className="text-sm font-medium text-yellow-600">43 (2%)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Inactive</span>
                    <span className="text-sm font-medium text-gray-600">298 (10%)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Retired</span>
                    <span className="text-sm font-medium text-orange-600">50 (2%)</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Asset Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Computers</span>
                    <span className="text-sm font-medium">1,245</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Mobile Devices</span>
                    <span className="text-sm font-medium">567</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Printers</span>
                    <span className="text-sm font-medium">234</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Networking</span>
                    <span className="text-sm font-medium">156</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Other</span>
                    <span className="text-sm font-medium">645</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Warranty Alerts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">23</div>
                    <div className="text-sm text-muted-foreground">Expiring this month</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">67</div>
                    <div className="text-sm text-muted-foreground">Expiring in 3 months</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tickets" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Ticket Volume</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-red-600">45</div>
                      <div className="text-sm text-muted-foreground">High Priority</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-yellow-600">67</div>
                      <div className="text-sm text-muted-foreground">Medium Priority</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">44</div>
                      <div className="text-sm text-muted-foreground">Low Priority</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>SLA Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Response Time</span>
                      <span className="text-sm font-medium">94.2%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '94.2%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Resolution Time</span>
                      <span className="text-sm font-medium">87.8%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '87.8%' }}></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Schedule</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">This Week</span>
                    <span className="text-sm font-medium">15 scheduled</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Next Week</span>
                    <span className="text-sm font-medium">23 scheduled</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">This Month</span>
                    <span className="text-sm font-medium">89 scheduled</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Overdue</span>
                    <span className="text-sm font-medium text-red-600">7 items</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Maintenance Costs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold">$24,567</div>
                    <div className="text-sm text-muted-foreground">This Month</div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold">$18,230</div>
                      <div className="text-xs text-muted-foreground">Preventive</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold">$6,337</div>
                      <div className="text-xs text-muted-foreground">Corrective</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}