import { createBrowserRouter } from 'react-router';
import Layout from '@/components/layout/Layout';
import * as Pages from '../pages';
import { ProtectedRoute } from './ProtectedRoute';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Pages.Home />,
      },
      {
        path: 'pricing',
        element: <Pages.Home />,
      },
      {
        path: 'contact',
        element: <Pages.Home />,
      },
      {
        path: 'login',
        element: <Pages.Login />,
      },
      {
        element: <ProtectedRoute />,
        children: [
          {
            path: 'dashboard',
            element: <Pages.Dashboard />,
          },
          // CustomerOps Routes
          {
            path: 'customerops',
            element: <Pages.CustomerOpsPage />,
          },
          {
            path: 'customerops/customers',
            element: <Pages.CustomerOpsCustomersPage />,
          },
          {
            path: 'customerops/leads',
            element: <Pages.CustomerOpsLeadsPage />,
          },
          {
            path: 'customerops/deals',
            element: <Pages.CustomerOpsDealsPage />,
          },
          {
            path: 'customerops/quotes',
            element: <Pages.CustomerOpsDealsPage />, // Same as deals for now
          },
          // ServiceOps Routes
          {
            path: 'serviceops',
            element: <Pages.ServiceOpsPage />,
          },
          {
            path: 'serviceops/assets',
            element: <Pages.ServiceOpsAssetsPage />,
          },
          {
            path: 'serviceops/tickets',
            element: <Pages.ServiceOpsTicketsPage />,
          },
          {
            path: 'serviceops/assignments',
            element: <Pages.ServiceOpsAssetsPage />, // Same as assets for now
          },
          // Timesheets Routes
          {
            path: 'timesheets',
            element: <Pages.TimesheetsPage />,
          },
        ],
      },
    ],
  },
]);
