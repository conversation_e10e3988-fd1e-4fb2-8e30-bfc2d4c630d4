import { Header } from './Header/Header';
import { Outlet } from 'react-router';
import { Footer } from './Footer';
import { Breadcrumbs } from './Navigation/Breadcrumbs';
import { SessionTimeout } from '@/features/auth/components/SessionTimeout';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { ModuleSidebar } from './Sidebar/ModuleSidebar';

function Layout() {
  const { isLoggedIn } = useAuth();

  return (
    <div className='flex min-h-screen min-w-screen w-screen flex-col bg-background'>
      <Header />
      <div className='flex flex-1'>
        {/* Sidebar - only shown when logged in */}
        {isLoggedIn && <ModuleSidebar />}
        
        <main className='flex-1 bg-foreground/5'>
          {isLoggedIn && (
            <div className='border-b bg-background p-4'>
              <Breadcrumbs />
            </div>
          )}
          <div className='p-6'>
            <Outlet />
          </div>
        </main>
      </div>
      <Footer />
      {isLoggedIn && <SessionTimeout />}
    </div>
  );
}

export default Layout;
