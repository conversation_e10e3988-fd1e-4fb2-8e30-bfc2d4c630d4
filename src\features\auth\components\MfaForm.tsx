import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { mfaSchema, MfaFormValues } from '../schemas/authSchemas';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface MfaFormProps {
  onSubmit: (values: MfaFormValues) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export const MfaForm = ({ onSubmit, onCancel, isLoading }: MfaFormProps) => {
  const form = useForm<MfaFormValues>({
    resolver: zodResolver(mfaSchema),
    defaultValues: {
      mfa_code: '',
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
        <FormField
          control={form.control}
          name='mfa_code'
          render={({ field }) => (
            <FormItem>
              <FormLabel>MFA Code</FormLabel>
              <FormControl>
                <Input
                  placeholder='Enter code'
                  type='text'
                  inputMode='text'
                  autoComplete='one-time-code'
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormDescription>
                Enter the code from your authenticator app.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type='submit' disabled={isLoading} className='w-full'>
          {isLoading ? 'Verifying...' : 'Submit Code'}
        </Button>
        <Button
          type='button'
          variant='outline'
          onClick={onCancel}
          disabled={isLoading}
          className='w-full'
        >
          Cancel
        </Button>
      </form>
    </Form>
  );
};
