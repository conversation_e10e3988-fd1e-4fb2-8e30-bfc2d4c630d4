import { z } from "zod";

// Asset Status Enum
export const assetStatusSchema = z.enum([
  'active',
  'inactive',
  'maintenance',
  'retired',
  'disposed',
  'pending_disposal',
  'lost',
  'stolen'
], {
  required_error: "Asset status is required",
});

export type AssetStatus = z.infer<typeof assetStatusSchema>;

// Asset Category Enum
export const assetCategorySchema = z.enum([
  'computer',
  'laptop',
  'mobile_device',
  'tablet',
  'printer',
  'monitor',
  'networking',
  'server',
  'software_license',
  'vehicle',
  'furniture',
  'equipment',
  'tool',
  'other'
], {
  required_error: "Asset category is required",
});

export type AssetCategory = z.infer<typeof assetCategorySchema>;

// Asset Condition Enum
export const assetConditionSchema = z.enum([
  'excellent',
  'good',
  'fair',
  'poor',
  'broken'
], {
  required_error: "Asset condition is required",
});

export type AssetCondition = z.infer<typeof assetConditionSchema>;

// Depreciation Method Enum
export const depreciationMethodSchema = z.enum([
  'straight_line',
  'declining_balance',
  'sum_of_years',
  'units_of_production',
  'none'
], {
  required_error: "Depreciation method is required",
});

export type DepreciationMethod = z.infer<typeof depreciationMethodSchema>;

// Asset Maintenance Record Schema
export const assetMaintenanceSchema = z.object({
  id: z.string(),
  assetId: z.string(),
  entityId: z.string(),
  
  // Maintenance Details
  type: z.enum(['preventive', 'corrective', 'emergency', 'upgrade']),
  title: z.string().min(1, "Maintenance title is required"),
  description: z.string().optional(),
  
  // Scheduling
  scheduledDate: z.string().optional(),
  completedDate: z.string().optional(),
  
  // Cost and Parts
  cost: z.number().min(0).default(0),
  currency: z.string().default('USD'),
  partsUsed: z.string().optional(),
  
  // Personnel
  performedBy: z.string().optional(),
  performedByName: z.string().optional(),
  vendor: z.string().optional(),
  
  // Status and Notes
  status: z.enum(['scheduled', 'in_progress', 'completed', 'cancelled']),
  notes: z.string().optional(),
  
  // Metadata
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type AssetMaintenance = z.infer<typeof assetMaintenanceSchema>;

// Asset Assignment Schema
export const assetAssignmentSchema = z.object({
  id: z.string(),
  assetId: z.string(),
  entityId: z.string(),
  
  // Assignment Details
  assignedToUserId: z.string(),
  assignedToUserName: z.string(),
  assignedByUserId: z.string(),
  assignedByUserName: z.string(),
  
  // Dates
  assignedDate: z.string(),
  returnDate: z.string().optional(),
  expectedReturnDate: z.string().optional(),
  
  // Location and Purpose
  location: z.string().optional(),
  purpose: z.string().optional(),
  notes: z.string().optional(),
  
  // Status
  status: z.enum(['active', 'returned', 'overdue']),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type AssetAssignment = z.infer<typeof assetAssignmentSchema>;

// Asset Schema
export const assetSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // Basic Information
  name: z.string().min(1, "Asset name is required"),
  description: z.string().optional(),
  category: assetCategorySchema,
  status: assetStatusSchema,
  condition: assetConditionSchema,
  
  // Identification
  assetTag: z.string().optional(),
  serialNumber: z.string().optional(),
  barcode: z.string().optional(),
  
  // Manufacturer Information
  manufacturer: z.string().optional(),
  model: z.string().optional(),
  partNumber: z.string().optional(),
  
  // Financial Information
  purchasePrice: z.number().min(0).optional(),
  currentValue: z.number().min(0).optional(),
  currency: z.string().default('USD'),
  depreciationMethod: depreciationMethodSchema.default('straight_line'),
  usefulLife: z.number().min(0).optional(), // in years
  
  // Dates
  purchaseDate: z.string().optional(),
  warrantyExpiration: z.string().optional(),
  lastMaintenanceDate: z.string().optional(),
  nextMaintenanceDate: z.string().optional(),
  
  // Location
  location: z.string().optional(),
  building: z.string().optional(),
  floor: z.string().optional(),
  room: z.string().optional(),
  
  // Ownership and Assignment
  ownerId: z.string().optional(),
  ownerName: z.string().optional(),
  assignedToUserId: z.string().optional(),
  assignedToUserName: z.string().optional(),
  
  // Vendor Information
  vendor: z.string().optional(),
  vendorContact: z.string().optional(),
  
  // Technical Specifications
  specifications: z.record(z.string()).optional(),
  
  // Related Records
  maintenanceRecords: z.array(assetMaintenanceSchema).default([]),
  assignmentHistory: z.array(assetAssignmentSchema).default([]),
  
  // Metadata
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  imageUrl: z.string().url().optional(),
  documents: z.array(z.object({
    id: z.string(),
    name: z.string(),
    url: z.string(),
    type: z.string(),
    uploadedAt: z.string(),
  })).default([]),
  
  // Timestamps
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
  
  // Compliance and Tracking
  complianceNotes: z.string().optional(),
  lastAuditDate: z.string().optional(),
  nextAuditDate: z.string().optional(),
});

export type Asset = z.infer<typeof assetSchema>;

// Asset Form Schemas
export const assetFormSchema = assetSchema.omit({
  id: true,
  maintenanceRecords: true,
  assignmentHistory: true,
  currentValue: true, // calculated
  createdBy: true,
  createdAt: true,
  updatedAt: true,
  lastMaintenanceDate: true, // calculated
  nextMaintenanceDate: true, // calculated
  lastAuditDate: true,
}).extend({
  entityId: z.string().min(1, "Entity is required"),
  tags: z.array(z.string()),
});

export type AssetFormValues = z.infer<typeof assetFormSchema>;

// Asset Maintenance Form Schema
export const assetMaintenanceFormSchema = assetMaintenanceSchema.omit({
  id: true,
  assetId: true,
  createdBy: true,
  createdAt: true,
  updatedAt: true,
});

export type AssetMaintenanceFormValues = z.infer<typeof assetMaintenanceFormSchema>;

// Asset Assignment Form Schema
export const assetAssignmentFormSchema = assetAssignmentSchema.omit({
  id: true,
  assetId: true,
  createdAt: true,
  updatedAt: true,
});

export type AssetAssignmentFormValues = z.infer<typeof assetAssignmentFormSchema>;

// Asset Search and Filter Schemas
export const assetSearchSchema = z.object({
  query: z.string().optional(),
  category: assetCategorySchema.optional(),
  status: assetStatusSchema.optional(),
  condition: assetConditionSchema.optional(),
  assignedToUserId: z.string().optional(),
  location: z.string().optional(),
  manufacturer: z.string().optional(),
  entityId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  sortBy: z.enum(['name', 'category', 'status', 'purchaseDate', 'currentValue', 'createdAt', 'updatedAt']).default('updatedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

export type AssetSearchParams = z.infer<typeof assetSearchSchema>;

// Asset List Response Schema
export const assetListResponseSchema = z.object({
  assets: z.array(assetSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

export type AssetListResponse = z.infer<typeof assetListResponseSchema>;

// Asset Statistics Schema
export const assetStatsSchema = z.object({
  totalAssets: z.number(),
  activeAssets: z.number(),
  inMaintenanceAssets: z.number(),
  totalValue: z.number(),
  averageAge: z.number(), // in years
  
  statusBreakdown: z.array(z.object({
    status: assetStatusSchema,
    count: z.number(),
    percentage: z.number(),
  })),
  
  categoryBreakdown: z.array(z.object({
    category: assetCategorySchema,
    count: z.number(),
    percentage: z.number(),
  })),
  
  conditionBreakdown: z.array(z.object({
    condition: assetConditionSchema,
    count: z.number(),
    percentage: z.number(),
  })),
  
  upcomingMaintenance: z.array(z.object({
    assetId: z.string(),
    assetName: z.string(),
    maintenanceDate: z.string(),
    type: z.string(),
  })),
  
  expiringWarranties: z.array(z.object({
    assetId: z.string(),
    assetName: z.string(),
    warrantyExpiration: z.string(),
    daysRemaining: z.number(),
  })),
});

export type AssetStats = z.infer<typeof assetStatsSchema>;

// Asset Audit Schema
export const assetAuditSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // Audit Details
  title: z.string().min(1, "Audit title is required"),
  description: z.string().optional(),
  auditType: z.enum(['physical', 'financial', 'compliance', 'lifecycle']),
  
  // Scope
  assetIds: z.array(z.string()).optional(),
  locations: z.array(z.string()).optional(),
  categories: z.array(assetCategorySchema).optional(),
  
  // Dates
  startDate: z.string(),
  endDate: z.string().optional(),
  plannedCompletionDate: z.string().optional(),
  
  // Personnel
  auditedBy: z.string(),
  auditedByName: z.string(),
  
  // Status and Results
  status: z.enum(['planned', 'in_progress', 'completed', 'cancelled']),
  findings: z.string().optional(),
  recommendations: z.string().optional(),
  
  // Assets Found/Missing
  assetsAudited: z.number().default(0),
  assetsFound: z.number().default(0),
  assetsMissing: z.number().default(0),
  discrepancies: z.array(z.object({
    assetId: z.string(),
    assetName: z.string(),
    expected: z.string(),
    found: z.string(),
    notes: z.string().optional(),
  })).default([]),
  
  // Metadata
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type AssetAudit = z.infer<typeof assetAuditSchema>;