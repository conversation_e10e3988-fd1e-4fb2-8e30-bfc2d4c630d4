import { useMutation } from '@tanstack/react-query';
import { ReactNode, useState, useEffect, useCallback, useRef } from 'react';
import AuthContext from './AuthContext';
import { LoginFormValues, LoginApiPayload, User, userSchema } from '../auth/schemas/authSchemas';
import { LoginResponse } from './AuthContext';

interface AuthProviderProps {
  children: ReactNode;
  loginUrl?: string;
  whoamiUrl?: string;
}

const AUTH_TOKEN_STORAGE_KEY = 'authToken';

export function AuthProvider({
  children,
  loginUrl = '/unprivileged/api/account/applogin',
  whoamiUrl = '/api/account/whoami',
}: AuthProviderProps) {
  const [authToken, setAuthToken] = useState<string | null>(null);
  const [isInitialising, setIsInitialising] = useState(true);
  const [isMfaRequired, setIsMfaRequired] = useState(false);
  const [pendingCredentials, setPendingCredentials] =
    useState<LoginFormValues | null>(null);
  const [user, setUser] = useState<User | null>(null);

  const onSuccessNavigateRef = useRef<(() => void) | undefined>(undefined);
  const onMfaRequiredRef = useRef<(() => void) | undefined>(undefined);

  const fetchUser = useCallback(async () => {
    if (!authToken) return;

    const userData = await fetch(whoamiUrl, {
      method: 'GET',
      headers: {
        'X-AUTH-TOKEN': authToken,
      },
    }).then(response => {
      if (!response.ok) {
        throw new Error('Failed to fetch user data');
      }
      return response.json();
    });
    
    // Parse user data with schema (fallback to mock data if parsing fails)
    try {
      const parsedUser = userSchema.parse(userData);
      setUser(parsedUser);
    } catch {
      // Fallback for development - create mock user data
      const mockUser: User = {
        id: userData.id || '1',
        email: userData.email || userData.Email || '<EMAIL>',
        firstName: userData.FirstName || userData.firstName || 'User',
        lastName: userData.LastName || userData.lastName || 'Name',
        entityId: userData.entityId || userData.EntityId || '1',
        roles: userData.roles || ['user'],
        permissions: userData.permissions || ['customerops:read', 'serviceops:read'],
        isActive: userData.isActive !== undefined ? userData.isActive : true,
      };
      setUser(mockUser);
    }
  }, [authToken, whoamiUrl]);

  useEffect(() => {
    try {
      const storedToken = localStorage.getItem(AUTH_TOKEN_STORAGE_KEY);
      if (storedToken) {
        setAuthToken(storedToken);
        fetchUser();
      }
    } catch (error) {
      console.error('Failed to retrieve auth token from storage:', error);
    } finally {
      setIsInitialising(false);
    }
  }, [fetchUser]);

  const loginMutation = useMutation<LoginResponse, Error, LoginApiPayload>({
    mutationFn: async (payload: LoginApiPayload) => {
      const response = await fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const responseBody = await response.json();

      if (!response.ok) {
        throw new Error(
          responseBody?.message ||
            `Login/MFA failed with status: ${response.status}`
        );
      }
      return responseBody as LoginResponse;
    },
    onSuccess: (data, variables) => {
      if (data.status === 0 && data.token) {
        setAuthToken(data.token);
        try {
          localStorage.setItem(AUTH_TOKEN_STORAGE_KEY, data.token);
        } catch (error) {
          console.error('Failed to save auth token to storage:', error);
        }
        setIsMfaRequired(false);
        setPendingCredentials(null);
        fetchUser();
        onSuccessNavigateRef.current?.();
      } else if (data.status === 1 && data.token) {
        setAuthToken(data.token);
        try {
          localStorage.setItem(AUTH_TOKEN_STORAGE_KEY, data.token);
        } catch (error) {
          console.error('Failed to save auth token to storage:', error);
        }
        setIsMfaRequired(false);
        setPendingCredentials(null);
        fetchUser();
        onSuccessNavigateRef.current?.();
      } else if (data.status === 2) {
        setIsMfaRequired(true);
        setPendingCredentials(variables);
        onMfaRequiredRef.current?.();
      } else if (data.status === 3) {
        if (!variables.mfa_token) {
          const { ...initialCredentials } = variables;
          setPendingCredentials(initialCredentials);
          setIsMfaRequired(true);
          onMfaRequiredRef.current?.();
        } else {
          setIsMfaRequired(false);
          setPendingCredentials(null);
          setAuthToken(null);
          localStorage.removeItem(AUTH_TOKEN_STORAGE_KEY);
          loginMutation.reset();
        }
      } else if (data.status === 4) {
        setIsMfaRequired(false);
        setPendingCredentials(null);
        setAuthToken(null);
        localStorage.removeItem(AUTH_TOKEN_STORAGE_KEY);
        loginMutation.reset();
      } else {
        setIsMfaRequired(false);
        setPendingCredentials(null);
      }
    },
    onError: () => {
      setAuthToken(null);
      localStorage.removeItem(AUTH_TOKEN_STORAGE_KEY);
      setIsMfaRequired(false);
      setPendingCredentials(null);
    },
    onSettled: () => {
      onSuccessNavigateRef.current = undefined;
      onMfaRequiredRef.current = undefined;
    },
  });

  const login = useCallback(
    (
      credentials: LoginFormValues,
      options?: {
        onSuccessNavigate?: () => void;
        onMfaRequired?: () => void;
      }
    ) => {
      setIsMfaRequired(false);
      setPendingCredentials(null);
      loginMutation.reset();

      onSuccessNavigateRef.current = options?.onSuccessNavigate;
      onMfaRequiredRef.current = options?.onMfaRequired;

      loginMutation.mutate(credentials);
    },
    [loginMutation]
  );

  const submitMfa = useCallback(
    (mfaToken: string) => {
      if (!pendingCredentials) {
        setIsMfaRequired(false);
        return;
      }
      if (!mfaToken) {
        return;
      }
      loginMutation.mutate({
        ...pendingCredentials,
        mfa_token: mfaToken,
      });
    },
    [loginMutation, pendingCredentials]
  );

  const cancelMfa = useCallback(() => {
    setIsMfaRequired(false);
    setPendingCredentials(null);
    loginMutation.reset();
  }, [loginMutation]);

  const logout = useCallback(
    (navigate?: () => void) => {
      setAuthToken(null);
      setIsMfaRequired(false);
      setPendingCredentials(null);
      setUser(null);
      try {
        localStorage.removeItem(AUTH_TOKEN_STORAGE_KEY);
      } catch (error) {
        console.error('Failed to remove auth token from storage:', error);
      }
      loginMutation.reset();
      navigate?.();
    },
    [loginMutation]
  );

  const authContextValue = {
    authToken,
    user,
    login,
    submitMfa,
    cancelMfa,
    logout,
    isLoading: loginMutation.isPending,
    isError: loginMutation.isError,
    error: loginMutation.error,
    isLoggedIn: !isInitialising && !!authToken,
    isMfaRequired,
    message: null,
  };

  return (
    <AuthContext.Provider value={authContextValue}>
      {children}
    </AuthContext.Provider>
  );
}
