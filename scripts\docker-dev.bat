@echo off
REM Script to run React frontend with Docker alongside existing backend

echo 🚀 Starting Concentric Cloud React Frontend in Docker...

REM Check if backend is accessible
echo 🔍 Checking if backend is accessible at localhost:8080...
curl -f -s http://localhost:8080 >nul 2>&1
if errorlevel 1 (
    curl -f -s http://localhost:8080/api >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Backend not accessible at localhost:8080
        echo Please make sure your backend is running and accessible at http://localhost:8080
        set /p continue="Continue anyway? (y/N): "
        if /i not "%continue%"=="y" (
            echo 💡 To start your backend, run: docker-compose up -d in your backend directory
            pause
            exit /b 1
        )
    ) else (
        echo ✅ Backend is accessible at localhost:8080
    )
) else (
    echo ✅ Backend is accessible at localhost:8080
)

REM Try to connect to existing network, fall back to simple mode
docker network ls | findstr concentric_network >nul
if errorlevel 1 (
    goto simple_mode
)

docker ps | findstr concentric_app >nul
if errorlevel 1 (
    goto simple_mode
)

echo 🌐 Using shared Docker network (concentric_network)
echo 🏗️  Building and starting frontend container...
docker-compose -f docker-compose.standalone.yml up --build
goto end

:simple_mode
echo 🌐 Using host network mode (connecting to localhost:8080)
echo 🏗️  Building and starting frontend container...
docker-compose -f docker-compose.simple.yml up --build

:end
echo ✅ Frontend should be available at http://localhost:3000
echo 📡 API requests will be proxied to your backend
pause