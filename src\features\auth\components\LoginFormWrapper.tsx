import { AuthAlert } from './AuthAlert';
import { MfaForm } from './MfaForm';
import { LoginForm } from './LoginForm';
import { useAuthForm } from '../hooks/useAuthForm';
import AuthContext from '../AuthContext';
import { useContext } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal } from 'lucide-react';

export const LoginFormWrapper = () => {
  const {
    handleLoginSubmit,
    handleMfaSubmit,
    handleCancelMfa,
    isLoading,
    isError,
    error,
    isMfaRequired,
  } = useAuthForm();

  const { message } = useContext(AuthContext);

  return (
    <div className='max-w-md w-full mx-auto'>
      {message && (
        <Alert variant={isError ? 'destructive' : 'default'} className='mb-4'>
          <Terminal className='h-4 w-4' />
          <AlertTitle>
            {isMfaRequired ? 'MFA Failed' : 'Login Failed'}
          </AlertTitle>
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}
      {isError && <AuthAlert isMfaRequired={isMfaRequired} error={error} />}

      {isMfaRequired ? (
        <MfaForm
          onSubmit={handleMfaSubmit}
          onCancel={handleCancelMfa}
          isLoading={isLoading}
        />
      ) : (
        <LoginForm onSubmit={handleLoginSubmit} isLoading={isLoading} />
      )}
    </div>
  );
};

export default LoginFormWrapper;
