import { z } from "zod";

// Deal Status Enum
export const dealStatusSchema = z.enum([
  'prospecting',
  'qualification',
  'proposal',
  'negotiation',
  'closed_won',
  'closed_lost',
  'on_hold'
], {
  required_error: "Deal status is required",
});

export type DealStatus = z.infer<typeof dealStatusSchema>;

// Deal Priority Enum
export const dealPrioritySchema = z.enum(['low', 'medium', 'high', 'critical'], {
  required_error: "Deal priority is required",
});

export type DealPriority = z.infer<typeof dealPrioritySchema>;

// Quote Status Enum
export const quoteStatusSchema = z.enum([
  'draft',
  'pending_approval',
  'approved',
  'sent',
  'accepted',
  'rejected',
  'expired'
], {
  required_error: "Quote status is required",
});

export type QuoteStatus = z.infer<typeof quoteStatusSchema>;

// Quote Line Item Schema
export const quoteLineItemSchema = z.object({
  id: z.string(),
  quoteId: z.string(),
  productName: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  unitPrice: z.number().min(0, "Unit price must be positive"),
  discount: z.number().min(0).max(100, "Discount must be between 0 and 100").default(0),
  totalPrice: z.number().min(0),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type QuoteLineItem = z.infer<typeof quoteLineItemSchema>;

// Quote Schema
export const quoteSchema = z.object({
  id: z.string(),
  dealId: z.string(),
  entityId: z.string(),
  
  // Quote Details
  quoteNumber: z.string(),
  title: z.string().min(1, "Quote title is required"),
  description: z.string().optional(),
  status: quoteStatusSchema,
  
  // Customer Information
  customerId: z.string().optional(),
  customerName: z.string(),
  customerEmail: z.string().email().optional(),
  
  // Financial Details
  subtotal: z.number().min(0).default(0),
  taxRate: z.number().min(0).max(100).default(0),
  taxAmount: z.number().min(0).default(0),
  discountAmount: z.number().min(0).default(0),
  totalAmount: z.number().min(0).default(0),
  
  // Terms and Conditions
  terms: z.string().optional(),
  validUntil: z.string().optional(),
  paymentTerms: z.string().optional(),
  
  // Line Items
  lineItems: z.array(quoteLineItemSchema).default([]),
  
  // Approval Workflow
  approvalRequired: z.boolean().default(false),
  approvedBy: z.string().optional(),
  approvedAt: z.string().optional(),
  rejectedBy: z.string().optional(),
  rejectedAt: z.string().optional(),
  rejectionReason: z.string().optional(),
  
  // Metadata
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
  sentAt: z.string().optional(),
  acceptedAt: z.string().optional(),
});

export type Quote = z.infer<typeof quoteSchema>;

// Deal Schema
export const dealSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // Basic Information
  title: z.string().min(1, "Deal title is required"),
  description: z.string().optional(),
  status: dealStatusSchema,
  priority: dealPrioritySchema,
  
  // Customer and Lead Information
  customerId: z.string().optional(),
  customerName: z.string(),
  leadId: z.string().optional(),
  
  // Financial Details
  value: z.number().min(0, "Deal value must be positive"),
  probability: z.number().min(0).max(100, "Probability must be between 0 and 100"),
  currency: z.string().default('USD'),
  
  // Timeline
  expectedCloseDate: z.string().optional(),
  actualCloseDate: z.string().optional(),
  
  // Assignment
  assignedUserId: z.string().optional(),
  assignedUserName: z.string().optional(),
  
  // Sales Process
  stage: z.string().optional(),
  source: z.string().optional(),
  lostReason: z.string().optional(),
  
  // Quotes
  quotes: z.array(quoteSchema).default([]),
  
  // Metadata
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  
  // Analytics
  lastActivityDate: z.string().optional(),
  timeInStage: z.number().optional(), // days in current stage
});

export type Deal = z.infer<typeof dealSchema>;

// Deal Form Schemas
export const dealFormSchema = dealSchema.omit({
  id: true,
  quotes: true,
  createdAt: true,
  updatedAt: true,
  actualCloseDate: true,
  lastActivityDate: true,
  timeInStage: true,
}).extend({
  entityId: z.string().min(1, "Entity is required"),
  tags: z.array(z.string()),
});

export type DealFormValues = z.infer<typeof dealFormSchema>;

// Quote Form Schemas
export const quoteFormSchema = quoteSchema.omit({
  id: true,
  quoteNumber: true,
  lineItems: true,
  approvedBy: true,
  approvedAt: true,
  rejectedBy: true,
  rejectedAt: true,
  rejectionReason: true,
  createdBy: true,
  createdAt: true,
  updatedAt: true,
  sentAt: true,
  acceptedAt: true,
});

export type QuoteFormValues = z.infer<typeof quoteFormSchema>;

// Quote Line Item Form Schema
export const quoteLineItemFormSchema = quoteLineItemSchema.omit({
  id: true,
  quoteId: true,
  totalPrice: true,
  createdAt: true,
  updatedAt: true,
});

export type QuoteLineItemFormValues = z.infer<typeof quoteLineItemFormSchema>;

// Search and Filter Schemas
export const dealSearchSchema = z.object({
  query: z.string().optional(),
  status: dealStatusSchema.optional(),
  priority: dealPrioritySchema.optional(),
  assignedUserId: z.string().optional(),
  customerId: z.string().optional(),
  entityId: z.string().optional(),
  minValue: z.number().optional(),
  maxValue: z.number().optional(),
  tags: z.array(z.string()).optional(),
  sortBy: z.enum(['title', 'value', 'probability', 'expectedCloseDate', 'createdAt', 'updatedAt']).default('updatedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

export type DealSearchParams = z.infer<typeof dealSearchSchema>;

// Deal List Response Schema
export const dealListResponseSchema = z.object({
  deals: z.array(dealSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

export type DealListResponse = z.infer<typeof dealListResponseSchema>;

// Deal Pipeline Stats Schema
export const dealPipelineStatsSchema = z.object({
  totalDeals: z.number(),
  totalValue: z.number(),
  averageDealSize: z.number(),
  conversionRate: z.number(),
  averageTimeToClose: z.number(), // in days
  statuses: z.array(z.object({
    status: dealStatusSchema,
    count: z.number(),
    value: z.number(),
    percentage: z.number(),
  })),
  priorities: z.array(z.object({
    priority: dealPrioritySchema,
    count: z.number(),
    percentage: z.number(),
  })),
  monthlyTrend: z.array(z.object({
    month: z.string(),
    dealsWon: z.number(),
    value: z.number(),
  })),
});

export type DealPipelineStats = z.infer<typeof dealPipelineStatsSchema>;

// Quote Approval Schema
export const quoteApprovalSchema = z.object({
  quoteId: z.string(),
  action: z.enum(['approve', 'reject']),
  reason: z.string().optional(),
  notes: z.string().optional(),
});

export type QuoteApprovalValues = z.infer<typeof quoteApprovalSchema>;