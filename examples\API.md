# Concentric Cloud Platform API Documentation

## Overview
The Concentric Cloud Platform provides a comprehensive REST API for managing customers, services, assets, financials, and more. This API powers the platform's modular architecture across CustomerOps, ServiceOps, AccountOps, and other product modules.

## Authentication

### API Key Authentication
All API endpoints require authentication using the `X-AUTH-TOKEN` header.

```http
X-AUTH-TOKEN: your-api-token-here
```

The API key is validated against the `ApiKeys` entity with the following requirements:
- Token must exist in the system
- Token must not be expired
- Returns the associated user's email address for authorization

### Authentication Failure Response
```json
{
    "message": "API key authentication failed"
}
```
**Status Code:** `401 Unauthorized`

## Base URL Structure

The API endpoints are organized by product modules:
- Customer Operations: `/api/product.customerops/`
- Service Operations: `/api/product.serviceops/`
- Account Operations: `/accounts/api/` and `/api/product.accountops/`
- Privacy Operations: `/api/service.privacyops/`
- Workflow Operations: `/api/product.workflowops/`

## Common Response Formats

### Success Response
```json
{
    "status": 0,
    "data": "object or array",
    "message": "Success description"
}
```

### Error Response Codes
```json
{
    "status": 1,    // General error
    "status": 2,    // Permission denied  
    "status": 3,    // Record not found
    "status": -1,   // Permissions failure
    "status": -2,   // Internal validation error
    "status": -3,   // Operation failed
    "status": -4,   // Comment required
    "status": -5,   // Workflow failed
    "message": "Error description"
}
```

### Pagination
Most list endpoints support pagination:
```
?page=1&display=25&order_by=CreatedAt&order_direction=DESC
```

## Date & Time Formats
- **DateTime:** `Y-m-d H:i:s` (2024-01-15 14:30:00)
- **Date:** `Y-m-d` (2024-01-15)
- **Time:** `H:i` (14:30)

---

## Customer Operations API

### Get Contacts
**GET** `/api/product.customerops/contacts`

Get filtered list of contacts/customers.

**Query Parameters:**
- `contact_type_id` (int): Contact type filter (0=contact, >0=specific type, null=lead)
- `account_manager` (string): Account manager GUID
- `parent_contact` (string): Parent contact GUID
- `account_type` (int): Account type (0=Person, 1=Organization)
- `statuses` (string): Pipe-separated status GUIDs ("guid1|guid2|guid3")
- `search` (string): Search term
- `order_by` (string): Sort field (default: "CreatedAt")
- `order_direction` (string): Sort direction (default: "DESC")
- `page` (int): Page number (default: 1)
- `display` (int): Items per page (default: 25)
- `format` (string): "select2" for dropdown format

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "uuid-string",
            "AccountName": "Company Name",
            "FirstName": "John",
            "LastName": "Doe",
            "EmailAddress": "<EMAIL>",
            "PhoneNumber": "+**********",
            "AccountType": 1,
            "ContactType": "Lead",
            "CreatedAt": "2024-01-15 14:30:00"
        }
    ],
    "pagination": {
        "current_page": 1,
        "total_pages": 5,
        "total_items": 125
    }
}
```

### Get Contact by Account Code
**GET** `/api/product.customerops/contacts/{account_code}`

Get specific contact by account code.

**Response:**
```json
{
    "status": 0,
    "data": {
        "guid": "uuid-string",
        "AccountCode": "CUST001",
        "AccountName": "Company Name",
        "FirstName": "John",
        "LastName": "Doe",
        "EmailAddress": "<EMAIL>",
        "PhoneNumber": "+**********"
    }
}
```

### Create New Customer/Contact
**POST** `/api/product.customerops/new`

Create a new customer or contact.

**Request Body:**
```json
{
    "AccountName": "New Company Ltd",
    "AccountTypeId": 1,
    "ContactTypeId": 0,
    "FirstName": "Jane",
    "LastName": "Smith",
    "IsLead": false,
    "EmailAddress": "<EMAIL>",
    "PhoneNumber": "+**********"
}
```

**Response:**
```json
{
    "status": 0,
    "guid": "new-contact-guid",
    "message": "Contact created successfully"
}
```

### Get Leads
**GET** `/api/product.customerops/leads`

Get list of leads with same filtering options as contacts.

### Get Calls
**GET** `/api/product.customerops/calls`

Get filtered list of calls.

**Query Parameters:**
- `agent` (string): Agent GUID
- `contact` (string): Contact GUID
- `direction` (int): 0=inbound, 1=outbound, null=any
- `search` (string): Search term
- `is_completed` (boolean): Filter by completion status
- `order_by` (string): Sort field (default: "CallAt")
- `order_direction` (string): Sort direction (default: "DESC")
- `page` (int): Page number
- `display` (int): Items per page

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "call-guid",
            "contact_name": "John Doe",
            "call_from": "+**********",
            "call_to": "+**********",
            "call_at": "2024-01-15 14:30:00",
            "direction": 1,
            "subject": "Sales Follow-up",
            "notes": "Discussed pricing options",
            "call_is_completed": true
        }
    ]
}
```

### Create New Call
**POST** `/api/product.customerops/calls`

Create a new call record.

**Request Body:**
```json
{
    "contact_guid": "contact-uuid",
    "call_from": "+**********",
    "call_to": "+**********",
    "call_at": "2024-01-15 14:30:00",
    "direction": 1,
    "subject": "Sales Follow-up",
    "notes": "Discussed pricing options",
    "call_is_completed": false
}
```

**Response:**
```json
{
    "status": 0,
    "guid": "new-call-guid",
    "message": "Call created successfully"
}
```

### Get Contact Types
**GET** `/api/product.customerops/types`

Get list of available contact types.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "id": 1,
            "name": "Prospect"
        },
        {
            "id": 2,
            "name": "Customer"
        }
    ]
}
```

### Get Account Managers
**GET** `/api/product.customerops/account-managers`

Get list of account managers.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "user-guid",
            "name": "John Smith",
            "email": "<EMAIL>"
        }
    ]
}
```

---

## Deal Management API

### Get Deals
**GET** `/api/product.customerops/deals`

Get list of deals with filtering options.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "deal-guid",
            "name": "Deal Name",
            "value": 50000.00,
            "probability": 75,
            "stage": "Negotiation",
            "expected_close": "2024-02-15"
        }
    ]
}
```

### Get Quotes
**GET** `/api/product.customerops/quotes`

Get list of quotes.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "quote-guid",
            "quote_number": "QT001",
            "customer_name": "Company Name",
            "total_amount": 25000.00,
            "status": "Pending",
            "created_at": "2024-01-15 14:30:00"
        }
    ]
}
```

### Get Quote for Print
**GET** `/api/product.customerops/quote/{guid}/print`

Get printable quote data.

**Response:**
```json
{
    "status": 0,
    "data": {
        "quote_number": "QT001",
        "customer": {
            "name": "Company Name",
            "address": "123 Main St"
        },
        "lines": [
            {
                "description": "Product A",
                "quantity": 2,
                "unit_price": 500.00,
                "total": 1000.00
            }
        ],
        "total_amount": 1000.00
    }
}
```

### Get Quote Lines
**GET** `/api/product.customerops/quote/{guid}/lines`

Get quote line items.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "id": 1,
            "description": "Product A",
            "quantity": 2,
            "unit_price": 500.00,
            "total": 1000.00
        }
    ]
}
```

### Get Quote Totals
**GET** `/api/product.customerops/quote/{guid}/totals`

Get quote totals and calculations.

**Response:**
```json
{
    "status": 0,
    "data": {
        "subtotal": 1000.00,
        "tax": 100.00,
        "total": 1100.00,
        "discount": 0.00
    }
}
```

### Add Quote Line
**POST** `/api/product.customerops/quote/{guid}/line`

Add new line to quote.

**Request Body:**
```json
{
    "description": "Product B",
    "quantity": 1,
    "unit_price": 750.00
}
```

**Response:**
```json
{
    "status": 0,
    "line_id": 2,
    "message": "Line added successfully"
}
```

### Update Quote Line
**POST** `/api/product.customerops/quote/{guid}/line/{line_id}`

Update existing quote line.

**Request Body:**
```json
{
    "description": "Updated Product B",
    "quantity": 2,
    "unit_price": 700.00
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Line updated successfully"
}
```

### Delete Quote Line
**GET** `/api/product.customerops/quote/{guid}/line/{line_id}/delete`

Delete quote line.

**Response:**
```json
{
    "status": 0,
    "message": "Line deleted successfully"
}
```

---

## Service Operations API

### Get Queues
**GET** `/api/product.serviceops/queues`

Get list of service queues.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "queue-guid",
            "name": "Support Queue",
            "description": "General support tickets",
            "active": true
        }
    ]
}
```

### Get Queue
**GET** `/api/product.serviceops/queue/{guid}`

Get specific queue details.

**Response:**
```json
{
    "status": 0,
    "data": {
        "guid": "queue-guid",
        "name": "Support Queue",
        "description": "General support tickets",
        "active": true,
        "ticket_types": [
            {
                "guid": "type-guid",
                "name": "Bug Report"
            }
        ]
    }
}
```

### Associate Ticket Type with Queue
**GET** `/api/product.serviceops/queue/{queue_guid}/associate-ticket-type/{ticket_type_guid}`

Associate a ticket type with a queue.

**Response:**
```json
{
    "status": 0,
    "message": "Ticket type associated successfully"
}
```

### Disassociate Ticket Type from Queue
**GET** `/api/product.serviceops/queue/{queue_guid}/disassociate-ticket-type/{ticket_type_guid}`

Remove association between ticket type and queue.

**Response:**
```json
{
    "status": 0,
    "message": "Ticket type disassociated successfully"
}
```

### Search Tickets
**GET** `/api/product.serviceops/ticket/search`

Search tickets with filters.

**Query Parameters:**
- `search` (string): Search term
- `queue` (string): Queue GUID
- `status` (string): Status filter
- `assignee` (string): Assignee GUID
- `priority` (int): Priority level

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "ticket-guid",
            "ticket_number": "TK001",
            "subject": "System Issue",
            "status": "Open",
            "priority": 2,
            "assignee": "John Smith",
            "created_at": "2024-01-15 14:30:00"
        }
    ]
}
```

### Create Ticket
**POST** `/api/product.serviceops/ticket`

Create new service ticket.

**Request Body:**
```json
{
    "subject": "System Issue",
    "description": "Detailed description of the issue",
    "priority": 2,
    "queue_guid": "queue-guid",
    "ticket_type_guid": "type-guid",
    "customer_guid": "customer-guid"
}
```

**Response:**
```json
{
    "status": 0,
    "guid": "new-ticket-guid",
    "ticket_number": "TK002",
    "message": "Ticket created successfully"
}
```

### Get Ticket Types
**GET** `/api/product.serviceops/tickettypes`

Get list of ticket types.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "type-guid",
            "name": "Bug Report",
            "description": "Software bug reports",
            "active": true
        }
    ]
}
```

---

## Fixed Asset Management API

### Get Asset Categories
**GET** `/api/product.serviceops/assetmanager/categories`

Get list of asset categories.

**Response:**
```json
{
    "status": 0,
    "data": {
        "1": "Computers",
        "2": "Furniture",
        "3": "Vehicles"
    }
}
```

### Get Assets
**GET** `/api/product.serviceops/assetmanager/assets`

Get list of fixed assets.

**Query Parameters:**
- `category_id` (string): Category GUID filter
- `assigned_to_id` (string): Assignee GUID filter
- `search` (string): Search term

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "asset-guid",
            "name": "Dell Laptop",
            "asset_code": "DL001",
            "category": "Computers",
            "assigned_to": "John Smith",
            "purchase_date": "2024-01-15",
            "purchase_price": 1200.00,
            "status": "In Use"
        }
    ]
}
```

### Get Asset by Code
**GET** `/api/product.serviceops/assetmanager/assets/{code}`

Get specific asset by asset code.

**Response:**
```json
{
    "status": 0,
    "data": {
        "guid": "asset-guid",
        "name": "Dell Laptop",
        "asset_code": "DL001",
        "category": "Computers",
        "assigned_to": "John Smith",
        "purchase_date": "2024-01-15",
        "purchase_price": 1200.00,
        "status": "In Use",
        "serial_number": "ABC123",
        "warranty_expiry": "2027-01-15"
    }
}
```

### Create Fixed Asset
**POST** `/api/product.serviceops/assetmanager/fixedasset/new`

Create new fixed asset.

**Request Body:**
```json
{
    "name": "New Laptop",
    "category_id": 1,
    "purchase_date": "2024-01-15",
    "purchase_price": 1500.00,
    "serial_number": "XYZ789",
    "warranty_expiry": "2027-01-15"
}
```

**Response:**
```json
{
    "status": 0,
    "guid": "new-asset-guid",
    "asset_code": "DL002",
    "message": "Asset created successfully"
}
```

### Issue Asset
**POST** `/api/product.serviceops/assetmanager/asset/{guid}/issue`

Issue asset to user.

**Request Body:**
```json
{
    "assign_to_guid": "user-guid",
    "due_back_date": "2024-12-31",
    "issue_notes": "Issued for project work"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Asset issued successfully"
}
```

### Create Asset Type
**POST** `/api/product.serviceops/assetmanager/types/new`

Create new asset type.

**Request Body:**
```json
{
    "name": "Mobile Device",
    "description": "Smartphones and tablets"
}
```

**Response:**
```json
{
    "status": 0,
    "guid": "new-type-guid",
    "message": "Asset type created successfully"
}
```

### Get Asset Types
**GET** `/api/product.serviceops/assetmanager/types`

Get list of asset types.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "type-guid",
            "name": "Mobile Device",
            "description": "Smartphones and tablets"
        }
    ]
}
```

### Create Asset Category
**POST** `/api/product.serviceops/assetmanager/fixedassetcategory/new`

Create new asset category.

**Request Body:**
```json
{
    "name": "Network Equipment",
    "description": "Routers, switches, and networking hardware"
}
```

**Response:**
```json
{
    "status": 0,
    "guid": "new-category-guid",
    "message": "Asset category created successfully"
}
```

---

## Timesheet Management API

### Log Time Entry
**POST** `/api/product.serviceops/timesheets/timelog`

Create new time log entry.

**Request Body:**
```json
{
    "user": "user-guid",
    "timesheet_account_code": "ACC001",
    "type_id": 1,
    "start_time": "09:00",
    "end_time": "17:00",
    "hours": "8:00",
    "entry_date": "2024-01-15",
    "description": "Development work on project X"
}
```

**Response:**
```json
{
    "status": 0,
    "guid": "timelog-guid",
    "message": "Time logged successfully"
}
```

### Get Time Log
**GET** `/api/product.serviceops/timesheets/timelog/{id}`

Get specific time log entry.

**Response:**
```json
{
    "status": 0,
    "data": {
        "guid": "timelog-guid",
        "user_name": "John Smith",
        "account_code": "ACC001",
        "type_name": "Development",
        "start_time": "09:00",
        "end_time": "17:00",
        "hours": "8:00",
        "entry_date": "2024-01-15",
        "description": "Development work on project X",
        "is_closed": false
    }
}
```

### Update Time Log
**POST** `/api/product.serviceops/timesheets/timelog/{id}`

Update existing time log entry.

**Request Body:**
```json
{
    "start_time": "09:30",
    "end_time": "17:30",
    "hours": "8:00",
    "description": "Updated description"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Time log updated successfully"
}
```

### Close Time Log
**GET** `/api/product.serviceops/timesheets/timelog/{id}/close`

Close/finalize time log entry.

**Response:**
```json
{
    "status": 0,
    "message": "Time log closed successfully"
}
```

### Delete Time Log
**GET** `/api/product.serviceops/timesheets/timelog/{id}/delete`

Delete time log entry.

**Response:**
```json
{
    "status": 0,
    "message": "Time log deleted successfully"
}
```

### Get Open Timer
**GET** `/api/product.serviceops/timesheets/timelog/opentimer`

Get currently running timer for user.

**Response:**
```json
{
    "status": 0,
    "data": {
        "guid": "timer-guid",
        "start_time": "09:00",
        "elapsed_time": "02:30",
        "description": "Current task"
    }
}
```

### List Time Logs
**GET** `/api/product.serviceops/timesheets/timelog/list`

Get list of time logs with filtering.

**Query Parameters:**
- `user` (string): User GUID
- `account_code` (string): Account code
- `date_from` (string): Start date filter
- `date_to` (string): End date filter

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "timelog-guid",
            "user_name": "John Smith",
            "account_code": "ACC001",
            "hours": "8:00",
            "entry_date": "2024-01-15",
            "description": "Development work"
        }
    ]
}
```

### Get Timesheet Accounts
**GET** `/api/product.serviceops/timesheets/accounts`

Get list of timesheet accounts.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "code": "ACC001",
            "name": "Project Alpha",
            "active": true
        }
    ]
}
```

### Get Work Types
**GET** `/api/product.serviceops/timesheets/worktypes`

Get list of work types.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "id": 1,
            "name": "Development",
            "active": true
        }
    ]
}
```

### Get Timesheet Users
**GET** `/api/product.serviceops/timesheets/users`

Get list of users for timesheet management.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "user-guid",
            "name": "John Smith",
            "email": "<EMAIL>",
            "active": true
        }
    ]
}
```

---

## Accounting Operations API

### Journal Management

#### Create Journal Entry
**GET** `/accounts/api/journal/new`

Create new journal entry.

**Response:**
```json
{
    "status": 0,
    "journal_id": 123,
    "message": "Journal entry created"
}
```

#### Get Journal Entry
**GET** `/accounts/api/journal/{journal_id}`

Get specific journal entry.

**Response:**
```json
{
    "status": 0,
    "data": {
        "journal_id": 123,
        "reference": "JE001",
        "date": "2024-01-15",
        "description": "Monthly adjustments",
        "total_debit": 1000.00,
        "total_credit": 1000.00,
        "ledger_entries": [
            {
                "account_code": "1000",
                "description": "Cash",
                "debit": 1000.00,
                "credit": 0.00
            }
        ]
    }
}
```

#### Update Journal Entry
**POST** `/accounts/api/journal/{journal_id}/update`

Update journal entry.

**Request Body:**
```json
{
    "reference": "JE001-Updated",
    "description": "Updated description",
    "date": "2024-01-15"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Journal entry updated successfully"
}
```

#### Reconcile Journal
**GET** `/accounts/api/journal/{journal_id}/reconcile`

Reconcile journal entry.

**Response:**
```json
{
    "status": 0,
    "message": "Journal reconciled successfully"
}
```

#### Add Ledger Entry
**GET** `/accounts/api/journal/{journal_id}/addledger`

Add new ledger entry to journal.

**Response:**
```json
{
    "status": 0,
    "ledger_id": 456,
    "message": "Ledger entry added"
}
```

### Ledger Management

#### Get Ledger Entry
**GET** `/accounts/api/ledger/{line_id}`

Get specific ledger entry.

**Response:**
```json
{
    "status": 0,
    "data": {
        "ledger_id": 456,
        "account_code": "1000",
        "account_name": "Cash",
        "description": "Payment received",
        "debit": 500.00,
        "credit": 0.00,
        "date": "2024-01-15"
    }
}
```

#### Update Ledger Entry
**POST** `/accounts/api/ledger/{ledger_id}/update`

Update ledger entry.

**Request Body:**
```json
{
    "account_code": "1000",
    "description": "Updated description",
    "debit": 600.00,
    "credit": 0.00
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Ledger entry updated successfully"
}
```

#### Delete Ledger Entry
**GET** `/accounts/api/ledger/{ledger_id}/delete`

Delete ledger entry.

**Response:**
```json
{
    "status": 0,
    "message": "Ledger entry deleted successfully"
}
```

### Tax Code Management

#### Get Tax Codes
**GET** `/accounts/api/taxcodes`

Get list of tax codes.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "code": "GST",
            "name": "Goods and Services Tax",
            "rate": 15.00,
            "active": true
        }
    ]
}
```

#### Get Tax Code
**GET** `/api/product.accountops/taxcodes/{tax_code}`

Get specific tax code details.

**Response:**
```json
{
    "status": 0,
    "data": {
        "code": "GST",
        "name": "Goods and Services Tax",
        "rate": 15.00,
        "active": true
    }
}
```

### Chart of Accounts

#### Search Chart of Accounts
**GET** `/accounts/chart/search/{query}`

Search chart of accounts.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "account_code": "1000",
            "account_name": "Cash",
            "account_type": "Asset",
            "balance": 5000.00
        }
    ]
}
```

#### Create New Account
**GET** `/accounts/chart/new/{type}`

Create new account of specified type.

**Response:**
```json
{
    "status": 0,
    "account_code": "1200",
    "message": "Account created successfully"
}
```

#### Get Account for Editing
**GET** `/accounts/chart/{account_code}/edit`

Get account details for editing.

**Response:**
```json
{
    "status": 0,
    "data": {
        "account_code": "1000",
        "account_name": "Cash",
        "account_type": "Asset",
        "parent_account": null,
        "active": true
    }
}
```

#### Update Account
**POST** `/accounts/chart/{account_code}/update`

Update account details.

**Request Body:**
```json
{
    "account_name": "Updated Cash Account",
    "account_type": "Asset",
    "parent_account": null,
    "active": true
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Account updated successfully"
}
```

### Invoice Management

#### Add Invoice Line
**GET** `/accounts/api/invoice/{invoice_number}/newline`

Add new line to invoice.

**Response:**
```json
{
    "status": 0,
    "line_id": 789,
    "message": "Invoice line added"
}
```

### Expense Management

#### Create Expense Preapproval
**POST** `/accounts/api/expense/preapproval/new`

Create new expense preapproval.

**Request Body:**
```json
{
    "employee_guid": "user-guid",
    "description": "Business travel expenses",
    "amount": 2000.00,
    "currency": "USD",
    "travel_date": "2024-02-01"
}
```

**Response:**
```json
{
    "status": 0,
    "preapproval_number": "PA001",
    "message": "Preapproval created successfully"
}
```

#### Create Expense Claim
**POST** `/accounts/api/expense/claim/new`

Create new expense claim.

**Request Body:**
```json
{
    "employee_guid": "user-guid",
    "preapproval_number": "PA001",
    "description": "Travel expenses - Conference",
    "total_amount": 1800.00,
    "currency": "USD",
    "expense_date": "2024-02-01"
}
```

**Response:**
```json
{
    "status": 0,
    "claim_number": "EC001",
    "message": "Expense claim created successfully"
}
```

#### Transition Preapproval State
**POST** `/accounts/api/expense/preapproval/{number}/transition`

Change preapproval state.

**Request Body:**
```json
{
    "new_state": "Approved",
    "comment": "Approved by manager"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Preapproval state updated"
}
```

#### Create Claim from Preapproval
**GET** `/accounts/api/expense/preapproval/{number}/createclaim`

Create claim from existing preapproval.

**Response:**
```json
{
    "status": 0,
    "claim_number": "EC002",
    "message": "Claim created from preapproval"
}
```

#### Get Receipt
**GET** `/accounts/api/expense/claim/{number}/receipt/{receipt_id}`

Get expense receipt.

**Response:**
```json
{
    "status": 0,
    "data": {
        "receipt_id": "receipt-guid",
        "filename": "receipt.pdf",
        "upload_date": "2024-01-15 14:30:00",
        "file_size": 256000
    }
}
```

#### Delete Receipt
**GET** `/accounts/api/expense/claim/{number}/receipt/{receipt_id}/delete`

Delete expense receipt.

**Response:**
```json
{
    "status": 0,
    "message": "Receipt deleted successfully"
}
```

#### Create Payment Journal
**GET** `/accounts/api/expense/claim/{number}/payjournal`

Create payment journal for expense claim.

**Response:**
```json
{
    "status": 0,
    "journal_id": 124,
    "message": "Payment journal created"
}
```

### Report Generation

#### Create Account Report
**POST** `/accounts/api/reports/create/{report_key}`

Generate accounting report.

**Request Body:**
```json
{
    "date_from": "2024-01-01",
    "date_to": "2024-01-31",
    "account_filter": "1000",
    "format": "pdf"
}
```

**Response:**
```json
{
    "status": 0,
    "report_url": "/reports/download/report123.pdf",
    "message": "Report generated successfully"
}
```

---

## Privacy Operations API

### Quick Filter Management

#### Create Quick Filter
**POST** `/api/service.privacyops/quickfilter/new`

Create new quick filter.

**Request Body:**
```json
{
    "Shared": false,
    "Name": "Active Customers",
    "ControllerName": "CustomerController",
    "SubmenuName": "contacts",
    "FilterSelected": ["status:active"],
    "Search": "",
    "SortBy": "CreatedAt",
    "SortDirection": "DESC",
    "DisplayColumns": ["AccountName", "EmailAddress", "PhoneNumber"]
}
```

**Response:**
```json
{
    "status": 0,
    "filter_id": "filter-guid",
    "message": "Quick filter created successfully"
}
```

#### Rename Quick Filter
**POST** `/api/service.privacyops/quickfilter/rename`

Rename existing quick filter.

**Request Body:**
```json
{
    "filter_id": "filter-guid",
    "new_name": "Updated Filter Name"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Quick filter renamed successfully"
}
```

#### Delete Quick Filter
**GET** `/api/service.privacyops/quickfilter/{id}/delete`

Delete quick filter.

**Response:**
```json
{
    "status": 0,
    "message": "Quick filter deleted successfully"
}
```

#### Update Quick Filter
**POST** `/api/service.privacyops/quickfilter/update`

Update quick filter settings.

**Request Body:**
```json
{
    "filter_id": "filter-guid",
    "FilterSelected": ["status:active", "type:customer"],
    "Search": "john",
    "SortBy": "LastName",
    "SortDirection": "ASC"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Quick filter updated successfully"
}
```

### Form Data Management

#### Update Form Data
**POST** `/api/service.privacyops/formdata/update`

Update form field data.

**Request Body:**
```json
{
    "TableName": "Customer",
    "RowGuid": "customer-guid",
    "Field": "EmailAddress",
    "NewValue": "<EMAIL>",
    "FieldType": "string",
    "DoctrineType": "string"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Form data updated successfully"
}
```

#### Get Key-Value Pair Data
**POST** `/api/service.privacyops/formdata/get/kvp`

Get key-value pair data.

**Request Body:**
```json
{
    "TableName": "Customer",
    "RowGuid": "customer-guid",
    "Field": "custom_fields"
}
```

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "key": "department",
            "value": "Sales"
        },
        {
            "key": "priority",
            "value": "High"
        }
    ]
}
```

#### Create Key-Value Pair
**POST** `/api/service.privacyops/formdata/new/kvp`

Create new key-value pair.

**Request Body:**
```json
{
    "TableName": "Customer",
    "RowGuid": "customer-guid",
    "Field": "custom_fields",
    "Key": "region",
    "Value": "North America"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Key-value pair created successfully"
}
```

#### Delete Key-Value Pair
**POST** `/api/service.privacyops/formdata/delete/kvp`

Delete key-value pair.

**Request Body:**
```json
{
    "TableName": "Customer",
    "RowGuid": "customer-guid",
    "Field": "custom_fields",
    "Key": "region"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Key-value pair deleted successfully"
}
```

### Event Management

#### Add Comment
**POST** `/api/service.privacyops/events/comments/new`

Add new comment to entity.

**Request Body:**
```json
{
    "TableName": "Ticket",
    "RowGuid": "ticket-guid",
    "Comment": "This is a status update comment"
}
```

**Response:**
```json
{
    "status": 0,
    "comment_id": "comment-guid",
    "message": "Comment added successfully"
}
```

#### Create State Transition
**POST** `/api/service.privacyops/events/transition/new`

Create new state transition.

**Request Body:**
```json
{
    "TableName": "Ticket",
    "RowGuid": "ticket-guid",
    "Comment": "Closing ticket - issue resolved",
    "TransitionId": "transition-guid"
}
```

**Response:**
```json
{
    "status": 0,
    "message": "State transition created successfully"
}
```

### External Share Management

#### Create External Share Link
**POST** `/api/product.privacyops/link`

Create external share link.

**Request Body:**
```json
{
    "entity_type": "Quote",
    "entity_guid": "quote-guid",
    "expires_at": "2024-02-15",
    "password_protected": true,
    "access_level": "read_only"
}
```

**Response:**
```json
{
    "status": 0,
    "share_url": "https://app.company.com/share/abc123",
    "share_guid": "share-guid",
    "message": "External share link created"
}
```

### Licensing

#### Check License
**POST** `/api/licensing/check`

Check license validity.

**Request Body:**
```json
{
    "product_name": "CustomerOps",
    "feature_name": "advanced_reporting"
}
```

**Response:**
```json
{
    "status": 0,
    "valid": true,
    "expires_at": "2024-12-31",
    "seats_available": 25,
    "seats_used": 15
}
```

---

## Workflow Operations API

### Execute Workflow
**GET** `/api/product.workflowops/workflows/execute/{guid}`

Execute workflow by GUID.

**Response:**
```json
{
    "status": 0,
    "execution_id": "exec-guid",
    "message": "Workflow executed successfully"
}
```

### Get State Workflows
**GET** `/api/product.workflowops/state-workflows`

Get list of state workflows.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "workflow-guid",
            "name": "Ticket Workflow",
            "description": "Standard ticket processing workflow",
            "states": [
                {
                    "id": 1,
                    "name": "Open",
                    "is_initial": true
                },
                {
                    "id": 2,
                    "name": "In Progress",
                    "is_initial": false
                },
                {
                    "id": 3,
                    "name": "Closed",
                    "is_initial": false
                }
            ]
        }
    ]
}
```

---

## File Management API

### Upload File
**POST** `/api/files/new`

Upload new file.

**Request Body:** (multipart/form-data)
```
file: [binary file data]
entity_type: "Customer"
entity_guid: "customer-guid"
description: "Customer contract document"
```

**Response:**
```json
{
    "status": 0,
    "file_guid": "file-guid",
    "filename": "contract.pdf",
    "message": "File uploaded successfully"
}
```

### Get Public File
**GET** `/app/public/{file_guid}`

Get public file by GUID.

**Response:**
Binary file data with appropriate Content-Type header.

---

## Meeting Management API

### Get Meetings
**GET** `/api/product.customerops/meetings`

Get list of meetings.

**Query Parameters:**
- `start_date` (string): Start date filter
- `end_date` (string): End date filter
- `attendee` (string): Attendee GUID filter
- `customer` (string): Customer GUID filter

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "guid": "meeting-guid",
            "subject": "Sales Meeting",
            "start_time": "2024-01-15 14:00:00",
            "end_time": "2024-01-15 15:00:00",
            "attendees": [
                {
                    "name": "John Smith",
                    "email": "<EMAIL>"
                }
            ],
            "customer": {
                "guid": "customer-guid",
                "name": "Acme Corp"
            }
        }
    ]
}
```

### Get Calendar
**GET** `/api/product.customerops/calendar`

Get calendar entries.

**Query Parameters:**
- `start` (string): Start date (Y-m-d)
- `end` (string): End date (Y-m-d)
- `user` (string): User GUID filter

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "id": "meeting-guid",
            "title": "Sales Meeting",
            "start": "2024-01-15T14:00:00",
            "end": "2024-01-15T15:00:00",
            "color": "#3788d8"
        }
    ]
}
```

---

## Push Notifications API

### Get User Notifications
**GET** `/api/push-notifications/pop/{destination}`

Get pending notifications for destination.

**Response:**
```json
{
    "status": 0,
    "data": [
        {
            "id": "notification-guid",
            "title": "New Ticket Assigned",
            "message": "Ticket TK001 has been assigned to you",
            "type": "ticket_assignment",
            "created_at": "2024-01-15 14:30:00",
            "read": false
        }
    ]
}
```

### Send Notification
**POST** `/api/push-notifications/send`

Send push notification.

**Request Body:**
```json
{
    "destination": "user-guid",
    "title": "System Alert",
    "message": "Your report is ready for download",
    "type": "report_ready",
    "action_url": "/reports/download/123"
}
```

**Response:**
```json
{
    "status": 0,
    "notification_id": "notification-guid",
    "message": "Notification sent successfully"
}
```

---

## System Management API

### User Management

#### Assign Role to User
**GET** `/api/manage-entity/users/{guid}/assign-role/{role_name}`

Assign role to user.

**Response:**
```json
{
    "status": 0,
    "message": "Role assigned successfully"
}
```

#### Revoke Role from User
**GET** `/api/manage-entity/users/{guid}/revoke-role/{role_name}`

Revoke role from user.

**Response:**
```json
{
    "status": 0,
    "message": "Role revoked successfully"
}
```

### Group Management

#### Add User to Group
**GET** `/api/manage-entity/groups/{group_guid}/add-user/{user_guid}`

Add user to group.

**Response:**
```json
{
    "status": 0,
    "message": "User added to group successfully"
}
```

#### Remove User from Group
**GET** `/api/manage-entity/groups/{group_guid}/remove-user/{user_guid}`

Remove user from group.

**Response:**
```json
{
    "status": 0,
    "message": "User removed from group successfully"
}
```

---

## Authentication & User Information API

### Get Current User
**GET** `/api/account/whoami`

Get current user information.

**Response:**
```json
{
    "status": 0,
    "data": {
        "guid": "user-guid",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Smith",
        "roles": ["ROLE_USER", "ROLE_ADMIN"],
        "entity": {
            "guid": "entity-guid",
            "name": "Company Name",
            "shortcode": "COMP"
        }
    }
}
```

### Get Entity Branch Server
**POST** `/api/getentitybranchserver`

Get entity branch server information.

**Request Body:**
```json
{
    "entity_shortcode": "COMP",
    "branch_code": "MAIN"
}
```

**Response:**
```json
{
    "status": 0,
    "data": {
        "server_url": "https://app.company.com",
        "branch_name": "Main Office",
        "timezone": "UTC"
    }
}
```

### Logout User
**GET** `/api/account/logout`

Logout current user.

**Response:**
```json
{
    "status": 0,
    "message": "Logged out successfully"
}
```

### Mobile App Login
**POST** `/unprivileged/api/account/applogin`

Mobile app login endpoint.

**Request Body:**
```json
{
    "username": "<EMAIL>",
    "password": "password123",
    "entity_shortcode": "COMP"
}
```

**Response:**
```json
{
    "status": 0,
    "auth_token": "api-token-here",
    "user": {
        "guid": "user-guid",
        "email": "<EMAIL>",
        "name": "John Smith"
    }
}
```

---

## Background Jobs API

### Authenticate Server Job
**POST** `/unprivileged/api/serverjobs/authenticate`

Authenticate background job process.

**Request Body:**
```json
{
    "job_token": "job-token-here",
    "server_id": "server-guid"
}
```

**Response:**
```json
{
    "status": 0,
    "authenticated": true,
    "job_details": {
        "job_id": "job-guid",
        "job_type": "data_export",
        "parameters": {}
    }
}
```

### Submit Job Result
**POST** `/unprivileged/api/serverjobs/job-result`

Submit background job result.

**Request Body:**
```json
{
    "job_id": "job-guid",
    "status": "completed",
    "result_data": {
        "output_file": "export_123.csv",
        "records_processed": 1500
    },
    "error_message": null
}
```

**Response:**
```json
{
    "status": 0,
    "message": "Job result submitted successfully"
}
```

---

## Error Handling

### Common Error Responses

#### Authentication Errors
```json
{
    "message": "API key authentication failed"
}
```
**Status Code:** `401 Unauthorized`

#### Permission Errors
```json
{
    "status": -1,
    "message": "Permission denied"
}
```
**Status Code:** `403 Forbidden`

#### Validation Errors
```json
{
    "status": -2,
    "message": "Validation failed: Email address is required",
    "errors": [
        {
            "field": "EmailAddress",
            "message": "This field is required"
        }
    ]
}
```
**Status Code:** `400 Bad Request`

#### Not Found Errors
```json
{
    "status": 3,
    "message": "Record not found"
}
```
**Status Code:** `404 Not Found`

#### Server Errors
```json
{
    "status": -3,
    "message": "Internal server error occurred"
}
```
**Status Code:** `500 Internal Server Error`

---

## Rate Limiting

API calls are subject to rate limiting based on:
- API key tier
- Endpoint complexity
- Server load

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

---

## API Versioning

The API uses URL-based versioning with different product module paths:
- `/api/product.customerops/` - Customer Operations
- `/api/product.serviceops/` - Service Operations  
- `/api/product.accountops/` - Account Operations
- `/api/service.privacyops/` - Privacy Operations
- `/api/product.workflowops/` - Workflow Operations

---

## SDKs and Integration

### cURL Examples

#### Get Contacts
```bash
curl -X GET "https://app.company.com/api/product.customerops/contacts" \
  -H "X-AUTH-TOKEN: your-api-token-here" \
  -H "Content-Type: application/json"
```

#### Create New Customer
```bash
curl -X POST "https://app.company.com/api/product.customerops/new" \
  -H "X-AUTH-TOKEN: your-api-token-here" \
  -H "Content-Type: application/json" \
  -d '{
    "AccountName": "New Company Ltd",
    "AccountTypeId": 1,
    "FirstName": "Jane",
    "LastName": "Smith",
    "EmailAddress": "<EMAIL>"
  }'
```

#### Log Time Entry
```bash
curl -X POST "https://app.company.com/api/product.serviceops/timesheets/timelog" \
  -H "X-AUTH-TOKEN: your-api-token-here" \
  -H "Content-Type: application/json" \
  -d '{
    "user": "user-guid",
    "timesheet_account_code": "ACC001",
    "type_id": 1,
    "hours": "8:00",
    "entry_date": "2024-01-15",
    "description": "Development work"
  }'
```

### JavaScript/Node.js Example
```javascript
const apiToken = 'your-api-token-here';
const baseUrl = 'https://app.company.com';

async function getContacts() {
    const response = await fetch(`${baseUrl}/api/product.customerops/contacts`, {
        headers: {
            'X-AUTH-TOKEN': apiToken,
            'Content-Type': 'application/json'
        }
    });
    
    const data = await response.json();
    return data;
}

async function createCustomer(customerData) {
    const response = await fetch(`${baseUrl}/api/product.customerops/new`, {
        method: 'POST',
        headers: {
            'X-AUTH-TOKEN': apiToken,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(customerData)
    });
    
    const data = await response.json();
    return data;
}
```

### Python Example
```python
import requests

class ConcentricAPI:
    def __init__(self, base_url, api_token):
        self.base_url = base_url
        self.headers = {
            'X-AUTH-TOKEN': api_token,
            'Content-Type': 'application/json'
        }
    
    def get_contacts(self, **params):
        url = f"{self.base_url}/api/product.customerops/contacts"
        response = requests.get(url, headers=self.headers, params=params)
        return response.json()
    
    def create_customer(self, customer_data):
        url = f"{self.base_url}/api/product.customerops/new"
        response = requests.post(url, headers=self.headers, json=customer_data)
        return response.json()
    
    def log_time(self, time_data):
        url = f"{self.base_url}/api/product.serviceops/timesheets/timelog"
        response = requests.post(url, headers=self.headers, json=time_data)
        return response.json()

# Usage
api = ConcentricAPI('https://app.company.com', 'your-api-token-here')
contacts = api.get_contacts(search='john', page=1, display=25)
```

---

## Support

For API support and questions:
- Documentation: This API.md file
- Issues: Contact your system administrator
- Rate limits: Contact support for increased limits
- New features: Submit feature requests through your support channel

---

*This documentation covers all 129 API endpoints available in the Concentric Cloud Platform. For the most up-to-date information, please refer to the system's built-in API documentation or contact your system administrator.*