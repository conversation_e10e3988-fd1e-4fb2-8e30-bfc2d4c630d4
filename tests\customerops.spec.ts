import { test, expect } from '@playwright/test';

test.describe('Customer Operations', () => {
  test('should login and navigate to customers page', async ({ page }) => {
    // Navigate to the login page
    await page.goto('http://localhost:3000/login');
    
    // Wait for the login form to be visible
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Fill in login credentials
    await page.fill('input[name="entityCode"]', '25CDS');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'PlayWrightTest123!');
    
    // Submit login form
    await page.click('button[type="submit"]');
    
    // Wait for login to complete and redirect
    await page.waitForURL('**/dashboard', { timeout: 15000 });
    
    // Navigate to customers page
    await page.goto('http://localhost:3000/customerops/customers');
    
    // Wait for the customers page to load
    await page.waitForSelector('h2:has-text("Customers")', { timeout: 10000 });
    
    // Check if the page loaded correctly
    await expect(page.locator('h2')).toContainText('Customers');
    
    // Check if the search input is present
    await expect(page.locator('input[placeholder*="Search customers"]')).toBeVisible();
    
    // Check if the table is present
    await expect(page.locator('table')).toBeVisible();
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'tests/screenshots/customers-page.png' });
    
    // Check for any error messages
    const errorMessages = await page.locator('[class*="error"], [class*="Error"]').count();
    if (errorMessages > 0) {
      console.log('Error messages found:', await page.locator('[class*="error"], [class*="Error"]').allTextContents());
    }
    
    // Check network requests to the API
    const apiRequests = [];
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        apiRequests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        });
      }
    });
    
    // Wait a moment for any API calls to complete
    await page.waitForTimeout(3000);
    
    console.log('API Requests made:', apiRequests);
  });
});