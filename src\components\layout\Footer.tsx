import { Link } from 'react-router';

export function Footer() {
  return (
    <footer className='border-t bg-background'>
      <div className='container flex flex-col items-center justify-end gap-4 py-0 md:h-15 md:flex-row md:py-0'>
        <div className='flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0'>
          <nav className='flex gap-4 sm:gap-6'>
            <Link
              to='/legal'
              className='text-sm font-medium underline text-foreground transition-colors hover:no-underline hover:text-link-hover'
            >
              Legal
            </Link>
            <Link
              to='/privacy'
              className='text-sm font-medium underline text-foreground transition-colors hover:no-underline hover:text-link-hover'
            >
              Privacy
            </Link>
            <Link
              to='/about/'
              className='text-sm font-medium underline text-foreground transition-colors hover:no-underline hover:text-link-hover'
            >
              About
            </Link>
          </nav>
        </div>
        <p className='text-center text-sm text-muted-foreground md:text-left'>
          © 2018 - {new Date().getFullYear()} Concentric Digital Solutions Ltd
        </p>
      </div>
    </footer>
  );
}
