import { useState } from 'react';
import { 
  LayoutGrid, 
  List, 
  Plus, 
  Package
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { useAssetStats } from '../hooks/useAssets';
import { Asset } from '../schemas/assetSchemas';

import { AssetList } from './AssetList';
import { AssetTracker } from './AssetTracker';

export function AssetManagement() {
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [showAssetForm, setShowAssetForm] = useState(false);
  const [showAssignmentForm, setShowAssignmentForm] = useState(false);
  const [showMaintenanceForm, setShowMaintenanceForm] = useState(false);
  const [viewMode, setViewMode] = useState<'tracker' | 'list'>('tracker');

  const { data: assetStats } = useAssetStats();

  const handleCreateAsset = () => {
    setSelectedAsset(null);
    setShowAssetForm(true);
  };

  const handleEditAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setShowAssetForm(true);
  };

  const handleViewAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    // Could implement a detailed view dialog here
  };

  const handleAssignAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setShowAssignmentForm(true);
  };

  const handleScheduleMaintenance = (asset: Asset) => {
    setSelectedAsset(asset);
    setShowMaintenanceForm(true);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Asset Management</h1>
          <p className="text-lg text-muted-foreground">
            Track, manage, and maintain your organization's fixed assets
          </p>
        </div>
        <div className="flex gap-2">
          <div className="flex rounded-lg border bg-background p-1">
            <Button
              variant={viewMode === 'tracker' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('tracker')}
            >
              <LayoutGrid className="mr-2 h-4 w-4" />
              Tracker
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="mr-2 h-4 w-4" />
              List
            </Button>
          </div>
          <RoleGuard permissions={[PERMISSIONS['serviceops:write']]}>
            <Button onClick={handleCreateAsset}>
              <Plus className="mr-2 h-4 w-4" />
              Add Asset
            </Button>
          </RoleGuard>
        </div>
      </div>

      {/* Asset Overview Stats */}
      {assetStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Package className="h-4 w-4" />
                Total Assets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assetStats.totalAssets}</div>
              <p className="text-xs text-muted-foreground">
                {assetStats.activeAssets} active, {assetStats.inMaintenanceAssets} in maintenance
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(assetStats.totalValue)}</div>
              <p className="text-xs text-muted-foreground">Current portfolio value</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Utilization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {((assetStats.activeAssets / assetStats.totalAssets) * 100).toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">Assets in active use</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Avg. Age</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assetStats.averageAge.toFixed(1)} years</div>
              <p className="text-xs text-muted-foreground">Fleet average age</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'tracker' | 'list')}>
        <TabsList className="hidden">
          <TabsTrigger value="tracker">Tracker</TabsTrigger>
          <TabsTrigger value="list">List</TabsTrigger>
        </TabsList>

        <TabsContent value="tracker" className="space-y-4">
          <AssetTracker
            onViewAsset={handleViewAsset}
            onAssignAsset={handleAssignAsset}
            onScheduleMaintenance={handleScheduleMaintenance}
          />
        </TabsContent>

        <TabsContent value="list" className="space-y-4">
          <AssetList
            onCreateAsset={handleCreateAsset}
            onEditAsset={handleEditAsset}
            onViewAsset={handleViewAsset}
            onAssignAsset={handleAssignAsset}
            onScheduleMaintenance={handleScheduleMaintenance}
          />
        </TabsContent>
      </Tabs>

      {/* Asset Form Dialog - Placeholder */}
      {showAssetForm && (
        <Dialog open={showAssetForm} onOpenChange={setShowAssetForm}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>
                {selectedAsset ? 'Edit Asset' : 'Create Asset'}
              </DialogTitle>
            </DialogHeader>
            <div className="p-4">
              <p className="text-muted-foreground mb-4">
                Asset form component would go here. This would include fields for:
                name, category, status, condition, serial number, purchase details, location, etc.
              </p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowAssetForm(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowAssetForm(false)}>
                  Save Asset
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Assignment Form Dialog - Placeholder */}
      {showAssignmentForm && (
        <Dialog open={showAssignmentForm} onOpenChange={setShowAssignmentForm}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Assign Asset</DialogTitle>
            </DialogHeader>
            <div className="p-4">
              <p className="text-muted-foreground mb-4">
                Asset assignment form would go here. Assign "{selectedAsset?.name}" to a user.
              </p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowAssignmentForm(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowAssignmentForm(false)}>
                  Assign Asset
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Maintenance Form Dialog - Placeholder */}
      {showMaintenanceForm && (
        <Dialog open={showMaintenanceForm} onOpenChange={setShowMaintenanceForm}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Schedule Maintenance</DialogTitle>
            </DialogHeader>
            <div className="p-4">
              <p className="text-muted-foreground mb-4">
                Maintenance scheduling form would go here. Schedule maintenance for "{selectedAsset?.name}".
              </p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowMaintenanceForm(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowMaintenanceForm(false)}>
                  Schedule
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}