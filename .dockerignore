# Dependencies
node_modules
.pnpm-store
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist
build
.next
out

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Documentation
README.md
*.md

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Testing
coverage
.nyc_output

# Logs
logs
*.log

# Temporary files
.tmp
.temp

# Cache directories
.cache
.parcel-cache
.next
.nuxt