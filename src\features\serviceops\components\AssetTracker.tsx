import { useState } from 'react';
import { 
  Package, 
  MapPin, 
  User, 
  <PERSON>ch, 
  Eye, 
  Filter,
  Refresh<PERSON>w,
  TrendingUp,
  <PERSON><PERSON><PERSON>riangle,
  Clock,
  DollarSign
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

import { useAssets, useAssetStats } from '../hooks/useAssets';
import { Asset, AssetSearchParams, AssetStatus, AssetCategory } from '../schemas/assetSchemas';

interface AssetTrackerProps {
  onViewAsset?: (asset: Asset) => void;
  onAssignAsset?: (asset: Asset) => void;
  onScheduleMaintenance?: (asset: Asset) => void;
}

export function AssetTracker({ onViewAsset, onAssignAsset, onScheduleMaintenance }: AssetTrackerProps) {
  const [searchParams, setSearchParams] = useState<AssetSearchParams>({
    page: 1,
    limit: 50,
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  });
  const [selectedCategory, setSelectedCategory] = useState<AssetCategory | 'all'>('all');
  const [selectedLocation, setSelectedLocation] = useState<string>('all');

  const { data: assetsData, isLoading, refetch } = useAssets({
    ...searchParams,
    ...(selectedCategory !== 'all' && { category: selectedCategory }),
    ...(selectedLocation !== 'all' && { location: selectedLocation }),
  });
  const { data: assetStats } = useAssetStats();

  const assets = assetsData?.assets || [];

  // Group assets by location
  const assetsByLocation = assets.reduce((acc, asset) => {
    const location = asset.location || 'Unassigned';
    if (!acc[location]) {
      acc[location] = [];
    }
    acc[location].push(asset);
    return acc;
  }, {} as Record<string, Asset[]>);

  // Get unique locations for filter
  const locations = Array.from(new Set(assets.map(asset => asset.location || 'Unassigned')));

  const handleSearch = (query: string) => {
    setSearchParams(prev => ({ ...prev, query, page: 1 }));
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: AssetStatus) => {
    const colors = {
      active: 'bg-green-100 text-green-800 border-green-200',
      inactive: 'bg-gray-100 text-gray-800 border-gray-200',
      maintenance: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      retired: 'bg-orange-100 text-orange-800 border-orange-200',
      disposed: 'bg-red-100 text-red-800 border-red-200',
      pending_disposal: 'bg-red-100 text-red-800 border-red-200',
      lost: 'bg-purple-100 text-purple-800 border-purple-200',
      stolen: 'bg-red-100 text-red-800 border-red-200',
    };
    return colors[status] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const AssetCard = ({ asset }: { asset: Asset }) => (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onViewAsset?.(asset)}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Package className="h-4 w-4 text-muted-foreground" />
              <h4 className="font-medium text-sm">{asset.name}</h4>
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              {asset.assetTag && (
                <div>Tag: {asset.assetTag}</div>
              )}
              {asset.serialNumber && (
                <div>S/N: {asset.serialNumber}</div>
              )}
            </div>
          </div>
          <Badge variant="outline" className={getStatusColor(asset.status)} style={{ fontSize: '10px' }}>
            {asset.status.replace('_', ' ')}
          </Badge>
        </div>

        <div className="space-y-2 text-xs">
          <div className="flex items-center gap-1">
            <Package className="h-3 w-3 text-muted-foreground" />
            <span className="capitalize">{asset.category.replace('_', ' ')}</span>
          </div>
          
          {asset.assignedToUserName && (
            <div className="flex items-center gap-1">
              <User className="h-3 w-3 text-muted-foreground" />
              <span>{asset.assignedToUserName}</span>
            </div>
          )}
          
          {(asset.currentValue || asset.purchasePrice) && (
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3 text-muted-foreground" />
              <span>{formatCurrency(asset.currentValue || asset.purchasePrice)}</span>
            </div>
          )}
          
          {asset.lastMaintenanceDate && (
            <div className="flex items-center gap-1">
              <Wrench className="h-3 w-3 text-muted-foreground" />
              <span>Last: {formatDate(asset.lastMaintenanceDate)}</span>
            </div>
          )}
        </div>

        <div className="flex gap-1 mt-3">
          <Button 
            size="sm" 
            variant="outline" 
            className="h-6 text-xs flex-1"
            onClick={(e) => {
              e.stopPropagation();
              onViewAsset?.(asset);
            }}
          >
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          {asset.status === 'active' && !asset.assignedToUserId && (
            <Button 
              size="sm" 
              variant="outline" 
              className="h-6 text-xs flex-1"
              onClick={(e) => {
                e.stopPropagation();
                onAssignAsset?.(asset);
              }}
            >
              <User className="h-3 w-3 mr-1" />
              Assign
            </Button>
          )}
          <Button 
            size="sm" 
            variant="outline" 
            className="h-6 text-xs flex-1"
            onClick={(e) => {
              e.stopPropagation();
              onScheduleMaintenance?.(asset);
            }}
          >
            <Wrench className="h-3 w-3 mr-1" />
            Maintain
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="animate-pulse bg-muted h-8 w-64 rounded"></div>
          <div className="animate-pulse bg-muted h-10 w-32 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-32 rounded"></div>
          ))}
        </div>
        <div className="animate-pulse bg-muted h-96 rounded"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Asset Tracker</h2>
          <p className="text-muted-foreground">
            Visual tracking and monitoring of your organization's assets
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Asset Statistics */}
      {assetStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Package className="h-4 w-4" />
                Total Assets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assetStats.totalAssets}</div>
              <div className="flex items-center gap-2 mt-1">
                <Progress value={(assetStats.activeAssets / assetStats.totalAssets) * 100} className="flex-1 h-2" />
                <span className="text-xs text-muted-foreground">
                  {((assetStats.activeAssets / assetStats.totalAssets) * 100).toFixed(1)}% active
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Total Value
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(assetStats.totalValue)}</div>
              <p className="text-xs text-muted-foreground">Current portfolio value</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Wrench className="h-4 w-4" />
                In Maintenance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assetStats.inMaintenanceAssets}</div>
              <p className="text-xs text-muted-foreground">
                {assetStats.upcomingMaintenance.length} upcoming
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Average Age
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assetStats.averageAge.toFixed(1)} years</div>
              <p className="text-xs text-muted-foreground">Fleet average</p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="location" className="space-y-4">
        <TabsList>
          <TabsTrigger value="location">By Location</TabsTrigger>
          <TabsTrigger value="category">By Category</TabsTrigger>
          <TabsTrigger value="status">By Status</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="location" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search assets..."
                    value={searchParams.query || ''}
                    onChange={(e) => handleSearch(e.target.value)}
                  />
                </div>
                <Select 
                  value={selectedCategory} 
                  onValueChange={(value) => setSelectedCategory(value as AssetCategory | 'all')}
                >
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="computer">Computer</SelectItem>
                    <SelectItem value="laptop">Laptop</SelectItem>
                    <SelectItem value="mobile_device">Mobile Device</SelectItem>
                    <SelectItem value="printer">Printer</SelectItem>
                    <SelectItem value="monitor">Monitor</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <Select 
                  value={selectedLocation} 
                  onValueChange={setSelectedLocation}
                >
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    {locations.map(location => (
                      <SelectItem key={location} value={location}>
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Assets by Location */}
          <div className="space-y-6">
            {Object.entries(assetsByLocation).map(([location, locationAssets]) => (
              <Card key={location}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      {location}
                    </CardTitle>
                    <Badge variant="secondary">
                      {locationAssets.length} asset{locationAssets.length > 1 ? 's' : ''}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {locationAssets.map((asset) => (
                      <AssetCard key={asset.id} asset={asset} />
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {Object.keys(assetsByLocation).length === 0 && (
              <Card>
                <CardContent className="py-8">
                  <div className="text-center text-muted-foreground">
                    No assets found matching your criteria.
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="category" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {assetStats?.categoryBreakdown.map((category) => (
              <Card key={category.category}>
                <CardHeader>
                  <CardTitle className="capitalize text-lg">
                    {category.category.replace('_', ' ')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">{category.count}</div>
                  <div className="flex items-center gap-2">
                    <Progress value={category.percentage} className="flex-1 h-2" />
                    <span className="text-sm text-muted-foreground">
                      {category.percentage.toFixed(1)}%
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="status" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {assetStats?.statusBreakdown.map((status) => (
              <Card key={status.status}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold">{status.count}</div>
                      <div className="text-sm capitalize text-muted-foreground">
                        {status.status.replace('_', ' ')}
                      </div>
                    </div>
                    <Badge variant="outline" className={getStatusColor(status.status)}>
                      {status.percentage.toFixed(1)}%
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Upcoming Maintenance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wrench className="h-5 w-5" />
                  Upcoming Maintenance
                </CardTitle>
              </CardHeader>
              <CardContent>
                {assetStats?.upcomingMaintenance.length ? (
                  <div className="space-y-2">
                    {assetStats.upcomingMaintenance.slice(0, 5).map((item) => (
                      <div key={item.assetId} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium">{item.assetName}</div>
                          <div className="text-sm text-muted-foreground">{item.type}</div>
                        </div>
                        <div className="text-sm">
                          {formatDate(item.maintenanceDate)}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-4">
                    No upcoming maintenance scheduled
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Expiring Warranties */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Expiring Warranties
                </CardTitle>
              </CardHeader>
              <CardContent>
                {assetStats?.expiringWarranties.length ? (
                  <div className="space-y-2">
                    {assetStats.expiringWarranties.slice(0, 5).map((item) => (
                      <div key={item.assetId} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium">{item.assetName}</div>
                          <div className="text-sm text-muted-foreground">
                            Expires: {formatDate(item.warrantyExpiration)}
                          </div>
                        </div>
                        <Badge 
                          variant={item.daysRemaining < 30 ? "destructive" : "secondary"}
                        >
                          {item.daysRemaining} days
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-4">
                    No warranties expiring soon
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}