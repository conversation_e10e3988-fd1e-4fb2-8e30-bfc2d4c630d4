# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `pnpm dev` - Start development server with Vite and hot module replacement
- `pnpm build` - Build for production (runs TypeScript compiler then Vite build)
- `pnpm lint` - Run ESLint on all files
- `pnpm preview` - Preview production build locally

## Project Architecture

This is a React 19 application built with Vite, TypeScript, and modern tooling. The project uses a feature-based architecture with the following key patterns:

### Core Stack
- **React Router 7** for client-side routing with nested layouts
- **TanStack Query** for server state management and API calls
- **Tailwind CSS 4** + **shadcn/ui** for styling and components
- **React Hook Form** + **Zod** for form handling and validation
- **pnpm** as the package manager

### Authentication System
The app implements a comprehensive authentication system with MFA support:
- Custom `AuthProvider` wraps the entire app and manages auth state
- Token-based authentication with localStorage persistence
- MFA flow support with multiple response status codes (0-4)
- Protected routes using `ProtectedRoute` wrapper component
- Auth context provides `login`, `logout`, `submitMfa`, and user state

### Project Structure
- `/src/features/` - Feature-based modules (e.g., `auth/` with components, hooks, schemas)
- `/src/components/ui/` - Reusable UI components (shadcn/ui based)
- `/src/components/layout/` - Layout components (Header, Footer, Layout)
- `/src/pages/` - Route pages exported from index.ts
- `/src/router/` - Router configuration and protected route logic
- `/src/lib/` - Utilities and shared configuration (queryClient, utils)

### Key Patterns
- Feature modules contain their own `components/`, `hooks/`, and `schemas/` subdirectories
- Components use Radix UI primitives with Tailwind styling
- API integration through TanStack Query mutations and queries
- Form schemas defined with Zod for type safety
- Absolute imports using `@/` alias pointing to `src/`

### Configuration
- Vite config includes `@/` path alias and Tailwind plugin
- ESLint configured for TypeScript with React hooks and refresh plugins
- Uses SWC for fast refresh instead of Babel