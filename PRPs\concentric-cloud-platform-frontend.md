# Concentric Cloud Platform React Frontend - Product Requirements Plan (PRP)

## Overview

Implement a comprehensive React frontend for the Concentric Cloud Platform, a multi-tenant business management system with integrated solutions for customer relationship management, service operations, financial management, and workflow automation.

## Critical Context for Implementation

### Existing Architecture Analysis

**Current Tech Stack:**
- React 19 with TypeScript and Vite
- React Router 7 for client-side routing with nested layouts
- TanStack Query for server state management and API calls
- Tailwind CSS 4 + shadcn/ui for styling and components
- React Hook Form + Zod for form handling and validation
- Comprehensive authentication system with MFA support

**Key Patterns Identified:**
- Feature-based architecture (`/src/features/`)
- Authentication: Token-based with localStorage persistence, MFA flow with status codes 0-4
- Forms: React Hook Form + Zod validation + shadcn/ui components
- API Integration: Native fetch API (no axios), TanStack Query mutations/queries
- Routing: Protected routes using `ProtectedRoute` wrapper component
- Components: shadcn/ui components with Radix UI primitives + Tailwind styling

### External Documentation References

**React Router 7 Documentation:** https://github.com/remix-run/react-router/blob/main/docs/
- Key patterns: Nested routes, protected routes, route modules, loaders
- Authentication integration: Route-based auth checking
- File structure: Route configurations in `routes.ts`

**TanStack Query Documentation:** https://github.com/tanstack/query/blob/main/docs/
- Mutations: Error handling, optimistic updates, cache invalidation
- Authentication: Token-based requests, error handling for auth failures
- Pattern: `useMutation` for data modifications, `useQuery` for fetching

**shadcn/ui Documentation:** https://github.com/shadcn-ui/ui/blob/main/apps/
- Form patterns: Form + FormField + Zod validation
- Installation: `npx shadcn@latest add [component]`
- Components needed: button, card, input, label, form, select, tabs, calendar, dropdown-menu

## Feature Implementation Plan

### Phase 1: Core Infrastructure & Authentication Enhancement

#### 1.1 Enhanced Authentication System
**Current State:** Basic auth with MFA support exists
**Requirements:**
- Multi-factor authentication with various methods
- Entity-based isolation for multi-tenancy
- Role-based access control (RBAC)
- Session management and timeout controls

**Implementation Tasks:**
1. Extend auth schemas to include entity/tenant information
2. Implement role-based permission checking
3. Add session timeout handling
4. Create user management components
5. Add API key management interface

**Files to Create/Modify:**
- `src/features/auth/schemas/authSchemas.ts` - Add RBAC and entity schemas
- `src/features/auth/components/RoleGuard.tsx` - Role-based component protection
- `src/features/auth/hooks/usePermissions.ts` - Permission checking hook
- `src/features/auth/components/SessionTimeout.tsx` - Session management

#### 1.2 Navigation & Layout System
**Requirements:**
- Multi-level navigation supporting all platform modules
- Responsive design with collapsible sidebars
- Breadcrumb navigation
- Quick access to major modules

**Implementation Tasks:**
1. Create main navigation component with module access
2. Implement breadcrumb navigation
3. Add responsive sidebar with module organization
4. Create module cards for dashboard

**Files to Create:**
- `src/components/layout/Navigation/MainNav.tsx`
- `src/components/layout/Navigation/Breadcrumbs.tsx`
- `src/components/layout/Navigation/ModuleCard.tsx`
- `src/components/layout/Sidebar/ModuleSidebar.tsx`

### Phase 2: CustomerOps Module

#### 2.1 Customer & Contact Management
**Requirements:**
- Comprehensive contact database with detailed profiles
- Account hierarchy support (parent/child relationships)
- Customer type categorization and segmentation
- Account manager assignments and relationship tracking

**Implementation Tasks:**
1. Create customer listing with search and filters
2. Implement customer detail forms with tabbed interface
3. Add contact management components
4. Create customer hierarchy visualization

**shadcn/ui Components Needed:**
- `npx shadcn@latest add table`
- `npx shadcn@latest add tabs`
- `npx shadcn@latest add dialog`
- `npx shadcn@latest add select`
- `npx shadcn@latest add badge`

**Files to Create:**
- `src/features/customerops/components/CustomerList.tsx`
- `src/features/customerops/components/CustomerForm.tsx`
- `src/features/customerops/components/ContactManager.tsx`
- `src/features/customerops/schemas/customerSchemas.ts`
- `src/features/customerops/hooks/useCustomers.ts`

#### 2.2 Lead Management
**Requirements:**
- Lead capture and qualification workflows
- Lead-to-customer conversion tracking
- Source attribution and campaign tracking
- Lead scoring and prioritization

**Implementation Tasks:**
1. Create lead capture forms
2. Implement lead qualification workflow
3. Add conversion tracking
4. Create lead scoring interface

**Files to Create:**
- `src/features/customerops/components/LeadForm.tsx`
- `src/features/customerops/components/LeadPipeline.tsx`
- `src/features/customerops/components/LeadScoring.tsx`

#### 2.3 Deal & Quote Management
**Requirements:**
- Sales pipeline management with stages
- Quote generation and approval workflows
- Deal probability and value tracking
- Quote line items with pricing and taxes

**Implementation Tasks:**
1. Create deal pipeline visualization
2. Implement quote builder with line items
3. Add approval workflow interface
4. Create deal tracking dashboard

**Files to Create:**
- `src/features/customerops/components/DealPipeline.tsx`
- `src/features/customerops/components/QuoteBuilder.tsx`
- `src/features/customerops/components/QuoteApproval.tsx`

### Phase 3: ServiceOps Module

#### 3.1 Fixed Asset Management
**Requirements:**
- Complete asset lifecycle tracking from acquisition to disposal
- Asset categorization with hierarchical organization
- Assignment tracking with due dates and return notifications
- Asset history and audit trails

**Implementation Tasks:**
1. Create asset listing with category filters
2. Implement asset detail forms with lifecycle tracking
3. Add assignment management with due date tracking
4. Create asset history timeline

**shadcn/ui Components Needed:**
- `npx shadcn@latest add calendar`
- `npx shadcn@latest add progress`
- `npx shadcn@latest add timeline` (if available)

**Files to Create:**
- `src/features/serviceops/components/AssetList.tsx`
- `src/features/serviceops/components/AssetForm.tsx`
- `src/features/serviceops/components/AssetAssignment.tsx`
- `src/features/serviceops/components/AssetHistory.tsx`

#### 3.2 Ticket Management
**Requirements:**
- Service desk ticket creation and management
- Ticket queue organization and routing
- SLA tracking and escalation procedures
- Ticket type configuration and workflows

**Implementation Tasks:**
1. Create ticket creation forms
2. Implement ticket queue management
3. Add SLA tracking with visual indicators
4. Create ticket workflow configuration

**Files to Create:**
- `src/features/serviceops/components/TicketForm.tsx`
- `src/features/serviceops/components/TicketQueue.tsx`
- `src/features/serviceops/components/SLATracker.tsx`

### Phase 4: Timesheets Module

#### 4.1 Personal Timesheet Management
**Requirements:**
- Daily time logging with project/task categorization
- Timer functionality for active work tracking
- Time entry editing and approval workflows
- Personal productivity tracking

**Implementation Tasks:**
1. Create timesheet entry interface with timer
2. Implement time tracking components
3. Add productivity analytics
4. Create approval workflow interface

**Files to Create:**
- `src/features/timesheets/components/TimesheetEntry.tsx`
- `src/features/timesheets/components/TimerWidget.tsx`
- `src/features/timesheets/components/TimeAnalytics.tsx`

### Phase 5: AccountOps Module

#### 5.1 Financial Management
**Requirements:**
- Cashbook management and bank reconciliation
- Invoice creation and management
- Expense management with approval workflows
- Budget management and variance analysis

**Implementation Tasks:**
1. Create invoice management interface
2. Implement expense tracking and approval
3. Add budget planning and tracking
4. Create financial reporting dashboard

**Files to Create:**
- `src/features/accountops/components/InvoiceManager.tsx`
- `src/features/accountops/components/ExpenseTracker.tsx`
- `src/features/accountops/components/BudgetPlanner.tsx`

### Phase 6: System Settings & Administration

#### 6.1 User & Entity Management
**Requirements:**
- User account creation and management
- Role assignment and permission control
- Entity hierarchy management
- Multi-tenant administration

**Implementation Tasks:**
1. Create user management interface
2. Implement role and permission management
3. Add entity configuration
4. Create admin dashboard

**Files to Create:**
- `src/features/systemsettings/components/UserManager.tsx`
- `src/features/systemsettings/components/RoleManager.tsx`
- `src/features/systemsettings/components/EntityConfig.tsx`

### Phase 7: Dashboard & Reporting

#### 7.1 Main Dashboard
**Requirements:**
- Product access hub with navigation cards
- Real-time notifications system
- Key metrics and analytics
- Quick actions and shortcuts

**Implementation Tasks:**
1. Create main dashboard with module cards
2. Implement notification system
3. Add metrics visualization
4. Create quick action components

**Files to Create:**
- `src/features/dashboard/components/DashboardHome.tsx`
- `src/features/dashboard/components/NotificationCenter.tsx`
- `src/features/dashboard/components/MetricCards.tsx`

## Technical Implementation Details

### API Integration Strategy

**Base URL Configuration:**
```typescript
// src/lib/api.ts
const API_BASE_URL = process.env.VITE_API_BASE_URL || 'http://localhost:8080';

export const apiClient = {
  async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('authToken');
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'X-AUTH-TOKEN': token }),
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }

    return response.json();
  }
};
```

### Error Handling Pattern
```typescript
// src/hooks/useApiError.ts
export const useApiError = () => {
  const navigate = useNavigate();
  const { logout } = useAuth();

  return useCallback((error: Error) => {
    if (error.message.includes('401')) {
      logout(() => navigate('/login'));
    }
    // Handle other error types
  }, [navigate, logout]);
};
```

### Form Validation Patterns
```typescript
// Example schema pattern
export const customerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  entityId: z.string().min(1, "Entity is required"),
  type: z.enum(['individual', 'business'], {
    required_error: "Customer type is required",
  }),
});
```

### Routing Structure
```typescript
// src/router/router.tsx - Extended route structure
export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      { index: true, element: <Pages.Home /> },
      { path: 'login', element: <Pages.Login /> },
      {
        element: <ProtectedRoute />,
        children: [
          { path: 'dashboard', element: <Pages.Dashboard /> },
          {
            path: 'customerops',
            children: [
              { index: true, element: <Pages.CustomerOps.Dashboard /> },
              { path: 'customers', element: <Pages.CustomerOps.Customers /> },
              { path: 'customers/:id', element: <Pages.CustomerOps.CustomerDetail /> },
              { path: 'leads', element: <Pages.CustomerOps.Leads /> },
              { path: 'deals', element: <Pages.CustomerOps.Deals /> },
            ],
          },
          // ... other module routes
        ],
      },
    ],
  },
]);
```

## Data Flow & State Management

### TanStack Query Integration
```typescript
// Example mutation pattern
export const useCreateCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (customer: CustomerFormValues) => 
      apiClient.request('/api/customers', {
        method: 'POST',
        body: JSON.stringify(customer),
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
    onError: (error) => {
      // Handle error
    },
  });
};
```

### Component Architecture Pattern
```typescript
// Feature component structure
export const CustomerList = () => {
  const { data: customers, isLoading, error } = useCustomers();
  const createCustomer = useCreateCustomer();
  
  if (isLoading) return <Skeleton />;
  if (error) return <Alert variant="destructive">{error.message}</Alert>;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Customers</CardTitle>
        <Button onClick={() => createCustomer.mutate(...)}>
          Add Customer
        </Button>
      </CardHeader>
      <CardContent>
        <DataTable data={customers} columns={customerColumns} />
      </CardContent>
    </Card>
  );
};
```

## Validation Gates & Quality Assurance

### Pre-Implementation Checklist
- [ ] All shadcn/ui components installed
- [ ] TanStack Query configured for each module
- [ ] Authentication guards implemented
- [ ] Form schemas defined with Zod
- [ ] API endpoints documented
- [ ] Error handling patterns established

### Testing Strategy
```bash
# Lint and type checking
pnpm lint && tsc --noEmit

# Build verification
pnpm build

# Component testing (when implemented)
pnpm test

# E2E testing (when implemented)
pnpm test:e2e
```

### Implementation Phases Validation
**Phase 1 Complete When:**
- [ ] Enhanced authentication system working
- [ ] Navigation system implemented
- [ ] All auth-related components tested

**Phase 2 Complete When:**
- [ ] Customer management fully functional
- [ ] Lead management workflow working
- [ ] Deal/quote management operational

**Subsequent Phases:**
- [ ] Each module fully implements CRUD operations
- [ ] All forms use proper validation
- [ ] Data fetching patterns consistent
- [ ] Error handling implemented
- [ ] Mobile responsiveness verified

## Potential Gotchas & Considerations

### Authentication Integration
- **Token Management:** Ensure proper token refresh and expiration handling
- **Multi-tenancy:** Verify entity isolation in API calls
- **Role Permissions:** Implement fine-grained permission checking

### Performance Considerations
- **Query Optimization:** Use TanStack Query caching effectively
- **Component Lazy Loading:** Implement code splitting for large modules
- **Data Pagination:** Handle large datasets properly

### shadcn/ui Integration
- **Theme Consistency:** Ensure all components follow design system
- **Accessibility:** Verify ARIA attributes and keyboard navigation
- **Form Validation:** Consistent error message patterns

### API Integration
- **Error Handling:** Standardize error responses across modules
- **Loading States:** Consistent loading indicators
- **Optimistic Updates:** Implement where appropriate

## Success Metrics

### Technical Metrics
- All TypeScript compilation passes without errors
- ESLint passes with no warnings
- Build process completes successfully
- All forms validate properly with Zod schemas

### Functional Metrics
- Each module implements full CRUD operations
- Authentication and authorization work correctly
- Multi-tenant isolation functions properly
- All major workflows are functional

### User Experience Metrics
- Responsive design works on mobile and desktop
- Loading states provide clear feedback
- Error messages are helpful and actionable
- Navigation is intuitive and consistent

## Quality Score: 9/10

**Confidence Level:** High confidence for one-pass implementation success due to:

✅ **Comprehensive Context:** Detailed existing architecture analysis
✅ **Clear Implementation Path:** Phased approach with specific tasks
✅ **Technical Documentation:** Real code examples and patterns
✅ **External References:** Specific URLs to documentation
✅ **Validation Gates:** Executable commands for verification
✅ **Existing Patterns:** Building on proven authentication system
✅ **Component Library:** shadcn/ui provides consistent components
✅ **State Management:** TanStack Query patterns established

**Potential Risk:** The scope is large, but the phased approach and existing foundation significantly reduce implementation complexity.

## Implementation Notes

1. **Start with Phase 1** to ensure solid foundation
2. **Use existing auth patterns** as template for other modules
3. **Follow established conventions** from current codebase
4. **Install shadcn/ui components** as needed per phase
5. **Test each phase** before proceeding to next
6. **Maintain consistency** with existing patterns and naming conventions

This PRP provides the comprehensive context and roadmap needed to successfully implement the Concentric Cloud Platform React frontend in a systematic, maintainable way.