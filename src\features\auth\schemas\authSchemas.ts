import { z } from "zod";

export const loginSchema = z.object({
  entity_code: z.string().min(2, {
    message: "Entity must be at least 2 characters.",
  }),
  email_address: z.string().email({
    message: "Please enter a valid email address.",
  }),
  password: z.string().min(1, {
    message: "Password is required.",
  }),
});

export type LoginFormValues = z.infer<typeof loginSchema>;

export const loginApiPayloadSchema = loginSchema.extend({
  mfa_token: z.string().optional(),
});

export type LoginApiPayload = z.infer<typeof loginApiPayloadSchema>;

export const mfaSchema = z.object({
  mfa_code: z.string().min(4, {
    message: "MFA code must be at least 4 characters.",
  }),
});

export type MfaFormValues = z.infer<typeof mfaSchema>;

// User and Entity schemas for RBAC
export const userSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
  entityId: z.string(),
  roles: z.array(z.string()),
  permissions: z.array(z.string()),
  isActive: z.boolean(),
});

export type User = z.infer<typeof userSchema>;

export const entitySchema = z.object({
  id: z.string(),
  code: z.string(),
  name: z.string(),
  parentId: z.string().optional(),
  type: z.enum(['root', 'tenant', 'department']),
  isActive: z.boolean(),
});

export type Entity = z.infer<typeof entitySchema>;

export const roleSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  permissions: z.array(z.string()),
  entityId: z.string(),
});

export type Role = z.infer<typeof roleSchema>;

// Permission constants
export const PERMISSIONS = {
  // CustomerOps
  'customerops:read': 'customerops:read',
  'customerops:write': 'customerops:write',
  'customerops:delete': 'customerops:delete',
  
  // ServiceOps
  'serviceops:read': 'serviceops:read',
  'serviceops:write': 'serviceops:write',
  'serviceops:delete': 'serviceops:delete',
  
  // Timesheets
  'timesheets:read': 'timesheets:read',
  'timesheets:write': 'timesheets:write',
  'timesheets:delete': 'timesheets:delete',
  'timesheets:approve': 'timesheets:approve',
  
  // AccountOps
  'accountops:read': 'accountops:read',
  'accountops:write': 'accountops:write',
  'accountops:approve': 'accountops:approve',
  
  // System Settings
  'system:admin': 'system:admin',
  'system:user_management': 'system:user_management',
  'system:entity_management': 'system:entity_management',
} as const;

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];
