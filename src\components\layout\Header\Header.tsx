import { Link } from 'react-router';
import { User, Menu } from 'lucide-react';
import { useContext } from 'react';
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@/components/ui/sheet';
import { SwitchTheme } from './switch-theme';
import AuthContext from '@/features/auth/AuthContext';
import { MainNav } from '../Navigation/MainNav';

interface HeaderProps {
  imagePath?: string;
}

export function Header({ imagePath }: HeaderProps) {
  const { isLoggedIn, logout, user } = useContext(AuthContext);
  const userName = user ? `${user.firstName} ${user.lastName}` : 'Guest User';

  return (
    <header className='w-full shadow-md border-b border-border/40 bg-background/95 supports-[backdrop-filter]:bg-background/60 dark:border-border/80 dark:bg-background/90'>
      <div className='container flex h-14 items-center max-w-full px-5'>
        {/* Mobile Menu and Logo */}
        <div className='flex items-center gap-4'>
          {/* Mobile menu button - moved to Layout for better organization */}

          {/* Logo */}
          {imagePath && (
            <div className='mr-4'>
              <img src={imagePath} alt='Logo' width={40} height={40} />
            </div>
          )}

          {/* Company Name */}
          <Link
            to={isLoggedIn ? '/dashboard' : '/'}
            className='font-semibold text-lg'
          >
            Concentric Cloud
          </Link>
        </div>

        {/* Main Navigation - Desktop */}
        {isLoggedIn ? (
          <MainNav />
        ) : (
          <NavigationMenu className='ml-4'>
            <NavigationMenuList>
              <NavigationMenuItem>
                <NavigationMenuLink asChild>
                  <Link
                    to='/'
                    className={`${navigationMenuTriggerStyle()} text-black`}
                  >
                    Home
                  </Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink asChild>
                  <Link
                    to='/pricing'
                    className={`${navigationMenuTriggerStyle()} text-black`}
                  >
                    Pricing
                  </Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink asChild>
                  <Link
                    to='/contact'
                    className={`${navigationMenuTriggerStyle()} text-black`}
                  >
                    Contact
                  </Link>
                </NavigationMenuLink>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
        )}

        {/* Right Side Controls */}
        <div className='flex flex-1 items-center justify-end space-x-4'>
          {isLoggedIn ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant='ghost'
                  className='relative h-8 w-auto space-x-0.5 px-1 hover:bg-accent/80 dark:hover:bg-accent/20'
                >
                  <User className='h-4 w-4 text-foreground/80 dark:text-foreground/90' />
                  <span className='hidden text-sm font-medium text-foreground/80 dark:text-foreground/90 sm:inline-block'>
                    {userName}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className='w-56 bg-popover dark:bg-popover/95'
                align='end'
                forceMount
              >
                <DropdownMenuLabel className='font-normal'>
                  <div className='flex flex-row justify-between space-y-1'>
                    <p className='text-sm font-medium leading-none text-foreground dark:text-foreground/90'>
                      {userName}
                    </p>
                    <SwitchTheme aria-label='Toggle theme' />
                  </div>
                </DropdownMenuLabel>

                <DropdownMenuSeparator className='bg-border/50 dark:bg-border/70' />
                <DropdownMenuItem
                  className='text-foreground/90 hover:bg-accent/80 dark:text-foreground/80 dark:hover:bg-accent/20'
                  onSelect={() => console.log('Navigate to Manage Account')}
                >
                  Manage Account
                </DropdownMenuItem>
                <DropdownMenuItem
                  className='text-foreground/90 hover:bg-accent/80 dark:text-foreground/80 dark:hover:bg-accent/20'
                  onSelect={() => console.log('Trigger Switch Entity')}
                >
                  Switch Entity
                </DropdownMenuItem>
                <DropdownMenuSeparator className='bg-border/50 dark:bg-border/70' />
                <DropdownMenuItem
                  className='text-foreground/90 hover:bg-accent/80 dark:text-foreground/80 dark:hover:bg-accent/20'
                  onSelect={() => logout()}
                >
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <>
              <SwitchTheme aria-label='Toggle theme' />
              <Link to='/login'>
                <Button
                  variant='ghost'
                  className='relative h-8 w-auto space-x-0.5 px-1 hover:bg-accent/80 dark:hover:bg-accent/20 py-3.5 px-3 flex-col justify-center'
                >
                  Login
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
