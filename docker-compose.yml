version: '3.8'

services:
  # React Frontend Development Service
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: concentric_frontend_dev
    restart: unless-stopped
    ports:
      - '3000:5173'  # Map host port 3000 to container port 5173
    volumes:
      # Bind mount source code for hot reload
      - .:/app:cached
      - /app/node_modules  # Anonymous volume for node_modules
      - /app/.pnpm-store   # Anonymous volume for pnpm store
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://host.docker.internal:8080
    networks:
      - concentric_network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # React Frontend Production Service
  frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: concentric_frontend_prod
    restart: unless-stopped
    ports:
      - '3001:80'  # Production on port 3001
    environment:
      - NODE_ENV=production
    networks:
      - concentric_network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 128M
          cpus: '0.25'
    profiles:
      - production  # Only start with --profile production

networks:
  # Connect to the existing network from your backend
  concentric_network:
    external: true