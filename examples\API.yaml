openapi: 3.0.3
info:
title: Concentric Cloud API
description: |
API for the Concentric Cloud platform.

    ## Authentication

    To use this API, you need to obtain an API token:

    1. **Get API Token**: Call `POST /unprivileged/api/account/applogin` with your credentials
    2. **Use Token**: Include the returned token in the `x-auth-token` header for all API requests

    Example:
    ```
    x-auth-token: 550e8400-e29b-41d4-a716-************
    ```

    The login endpoint does not require authentication, but all other endpoints do.

    ## Permission System

    The Concentric Cloud platform uses a hierarchical role-based access control (RBAC) system. Each user is assigned roles that determine their access to different features and data.

    ### Role Hierarchy

    Roles are organized hierarchically where higher-level roles inherit permissions from lower-level roles:

    - **ROLE_USER**: Basic system access (required for login)
    - **ROLE_STAFF**: Access to Concentric system
    - **ROLE_GUEST**: Customer access to Portal
    - **ROLE_ADMIN**: Complete administrative access (inherits all module permissions)

    ### Module-Specific Roles

    Each module has specific roles with different permission levels:

    **CustomerOps Module:**
    - `ROLE_CUSTOMEROPS`: Basic CustomerOps access
    - `ROLE_CUSTOMEROPS_ACCOUNTS`: Account management
    - `ROLE_CUSTOMEROPS_CONTACTS`: Contact management
    - `ROLE_CUSTOMEROPS_LEADS`: Lead management
    - `ROLE_CUSTOMEROPS_QUOTES`: Quote management
    - `ROLE_CUSTOMEROPS_SETTINGS`: CustomerOps settings
    - `ROLE_CUSTOMEROPS_FULL`: Complete CustomerOps access

    **Timesheets Module:**
    - `ROLE_TIMESHEETS`: Basic timesheet access
    - `ROLE_TIMESHEETS_USER`: User-level timesheet access
    - `ROLE_TIMESHEETS_EDITOR`: Can edit other users' timesheets
    - `ROLE_TIMESHEETS_ADMIN`: Administrative timesheet access
    - `ROLE_TIMESHEETS_FULL`: Complete timesheet access

    **EntitySetup Module:**
    - `ROLE_ENTITYSETUP`: Basic entity setup access
    - `ROLE_ENTITYSETUP_USERS`: User management
    - `ROLE_ENTITYSETUP_MANAGEROLES`: Role management
    - `ROLE_ENTITYSETUP_POLICIES`: Policy management
    - `ROLE_ENTITYSETUP_PERMISSIONS`: Permission management
    - `ROLE_ENTITYSETUP_LICENSING`: License management
    - `ROLE_ENTITYSETUP_STATEWORKFLOWS`: Workflow management
    - `ROLE_ENTITYSETUP_APPS`: Application management
    - `ROLE_ENTITYSETUP_CUSTOMISATION`: Customization settings
    - `ROLE_ENTITYSETUP_BILLING`: Billing management
    - `ROLE_ENTITYSETUP_FULL`: Complete entity setup access

    ### Permission Responses

    When you lack the required permissions for an endpoint, you will receive:

    **401 Unauthorized**: Authentication is missing or invalid
    ```json
    {
      "status": 401,
      "message": "Unauthorized access"
    }
    ```

    **403 Forbidden**: You are authenticated but lack the required permissions
    ```json
    {
      "status": 403,
      "message": "You do not have permission to perform this action. Please request access from the administrator"
    }
    ```

    ## Content Type Requirements

    **Important**: All API endpoints require the following headers:
    - `Content-Type: application/json` (for POST/PUT requests)
    - `Accept: application/json` (for all requests)

    Without these headers, the API may return HTML error pages instead of JSON responses.

version: 1.0.0
servers:

- url: http://localhost:8080
  description: Development Server
  tags:
- name: Customer Operations
  description: Customer and lead management operations
- name: Service Operations
  description: Service desk and queue management operations
- name: Timesheets
  description: Time tracking and timesheet management
- name: Privacy Operations
  description: Privacy and data management operations
- name: Workflow Operations
  description: Workflow and state management operations
- name: External Share
  description: External sharing and collaboration features
- name: File Operations
  description: File upload and management operations
- name: Licensing
  description: License validation and management
- name: Account Management
  description: User authentication and account management operations
- name: Authentication
  description: API token creation and authentication flows
  paths:

# Customer Operations

/api/product.customerops/contacts/{account_code}:
get:
tags: - Customer Operations
summary: Get contact by account code
description: |
Retrieves contact details by account code.

        **Required Role**: `ROLE_CUSTOMEROPS_CONTACTS` or higher
      parameters:
        - $ref: '#/components/parameters/ContentTypeJson'
        - $ref: '#/components/parameters/AcceptJson'
        - name: account_code
          in: path
          required: true
          schema:
            type: string
          description: Unique account code for the contact
      responses:
        '200':
          description: Contact details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: Contact not found

/api/product.customerops/contacts:
get:
tags: - Customer Operations
summary: Get contacts list with filtering
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: contact_type_id
in: query
schema:
type: integer - name: account_manager
in: query
schema:
type: string
format: uuid - name: parent_contact
in: query
schema:
type: string
format: uuid - name: account_type
in: query
schema:
type: integer - name: statuses
in: query
schema:
type: string - name: search
in: query
schema:
type: string - name: order_by
in: query
schema:
type: string
default: CreatedAt - name: order_direction
in: query
schema:
type: string
enum: [ASC, DESC]
default: DESC - name: page
in: query
schema:
type: integer
default: 1 - name: display
in: query
schema:
type: integer
default: 25
responses:
'200':
description: List of contacts
content:
application/json:
schema:
type: object
properties:
customers:
type: array
items:
$ref: '#/components/schemas/Customer'

/api/product.customerops/leads:
get:
tags: - Customer Operations
summary: Get leads list with filtering
description: |
Retrieves a filtered list of leads with pagination support.

        **Required Role**: `ROLE_CUSTOMEROPS_LEADS` or higher
      parameters:
        - $ref: '#/components/parameters/ContentTypeJson'
        - $ref: '#/components/parameters/AcceptJson'
        - name: account_manager
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by account manager UUID
        - name: parent_contact
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by parent contact UUID
        - name: account_type
          in: query
          schema:
            type: integer
          description: Filter by account type ID
        - name: statuses
          in: query
          schema:
            type: string
          description: Filter by lead statuses (comma-separated)
        - name: search
          in: query
          schema:
            type: string
          description: Search term for lead names or account codes
        - name: order_by
          in: query
          schema:
            type: string
            default: CreatedAt
          description: Field to order results by
        - name: order_direction
          in: query
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          description: Sort direction
        - name: page
          in: query
          schema:
            type: integer
            default: 1
          description: Page number for pagination
        - name: display
          in: query
          schema:
            type: integer
            default: 25
          description: Number of leads per page
      responses:
        '200':
          description: List of leads
          content:
            application/json:
              schema:
                type: object
                properties:
                  leads:
                    type: array
                    items:
                      $ref: '#/components/schemas/Customer'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'

/api/product.customerops/calls:
get:
tags: - Customer Operations
summary: Get calls list with filtering
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: agent
in: query
schema:
type: string
format: uuid - name: contact
in: query
schema:
type: string
format: uuid - name: direction
in: query
schema:
type: integer
enum: [0, 1] - name: search
in: query
schema:
type: string - name: order_by
in: query
schema:
type: string
default: CallAt - name: order_direction
in: query
schema:
type: string
enum: [ASC, DESC]
default: DESC - name: page
in: query
schema:
type: integer
default: 1 - name: display
in: query
schema:
type: integer
default: 25
responses:
'200':
description: List of calls
content:
application/json:
schema:
type: object
properties:
calls:
type: array
items:
$ref: '#/components/schemas/Call'
post:
tags: - Customer Operations
summary: Create new call
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
contact_guid:
type: string
format: uuid
call_from:
type: string
call_to:
type: string
subject:
type: string
notes:
type: string
direction:
type: integer
enum: [0, 1]
call_completed:
type: boolean
spoke_to:
type: string
responses:
'200':
description: Call created successfully
content:
application/json:
schema:
type: object
properties:
status:
type: integer
guid:
type: string
format: uuid
message:
type: string

/api/product.customerops/new:
post:
tags: - Customer Operations
summary: Create new customer/contact
description: |
Creates a new customer or contact record.

        **Required Role**: `ROLE_CUSTOMEROPS_CONTACTS` or higher (for contacts) or `ROLE_CUSTOMEROPS_LEADS` or higher (for leads)
      parameters:
        - $ref: '#/components/parameters/ContentTypeJson'
        - $ref: '#/components/parameters/AcceptJson'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                AccountName:
                  type: string
                  description: Name of the account/company
                AccountTypeId:
                  type: integer
                  description: Account type identifier
                ContactTypeId:
                  type: integer
                  description: Contact type identifier
                FirstName:
                  type: string
                  description: Contact's first name
                LastName:
                  type: string
                  description: Contact's last name
                IsLead:
                  type: boolean
                  description: Whether this is a lead (true) or customer (false)
              required:
                - AccountName
                - AccountTypeId
                - ContactTypeId
      responses:
        '200':
          description: Customer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  guid:
                    type: string
                    format: uuid
                  message:
                    type: string
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '422':
          $ref: '#/components/responses/ValidationError'

/api/product.customerops/types:
get:
tags: - Customer Operations
summary: Get customer types
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: List of customer types
content:
application/json:
schema:
type: object
additionalProperties:
type: string

/api/product.customerops/account-managers:
get:
tags: - Customer Operations
summary: Get account managers
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: List of account managers
content:
application/json:
schema:
type: object
additionalProperties:
type: string

/api/product.customerops/meetings:
get:
tags: - Customer Operations
summary: Get meetings with filtering
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: user_guid
in: query
schema:
type: string
format: uuid - name: contact_guid
in: query
schema:
type: string
format: uuid - name: from_datetime
in: query
schema:
type: string
format: date-time - name: to_datetime
in: query
schema:
type: string
format: date-time - name: internal_attendee
in: query
schema:
type: string
format: uuid - name: external_attendee
in: query
schema:
type: string
format: uuid - name: order_by
in: query
schema:
type: string
default: MeetingAt - name: order_direction
in: query
schema:
type: string
enum: [ASC, DESC]
default: ASC - name: page
in: query
schema:
type: integer
default: 1
responses:
'200':
description: List of meetings
content:
application/json:
schema:
type: object
properties:
meetings:
type: array
items:
$ref: '#/components/schemas/Meeting'

/api/product.customerops/calendar:
get:
tags: - Customer Operations
summary: Get calendar entries
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: Calendar entries
content:
application/json:
schema:
type: array
items:
type: object

# Service Operations

/api/product.serviceops/queues:
get:
tags: - Service Operations
summary: Get queues list
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: List of queues
content:
application/json:
schema:
type: object
additionalProperties:
type: string

/api/product.serviceops/queue/{guid}:
get:
tags: - Service Operations
summary: Get specific queue
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: guid
in: path
required: true
schema:
type: string
format: uuid
responses:
'200':
description: Queue details
content:
application/json:
schema:
$ref: '#/components/schemas/Queue'

/api/product.serviceops/ticket/search:
get:
tags: - Service Operations
summary: Search tickets
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: queues
in: query
schema:
type: string - name: ticket_types
in: query
schema:
type: string - name: statuses
in: query
schema:
type: string - name: search
in: query
schema:
type: string - name: order_by
in: query
schema:
type: string
default: CreatedAt - name: order_direction
in: query
schema:
type: string
enum: [ASC, DESC]
default: DESC - name: page
in: query
schema:
type: integer
default: 1 - name: display
in: query
schema:
type: integer
default: 25
responses:
'200':
description: List of tickets
content:
application/json:
schema:
type: object
properties:
tickets:
type: array
items:
$ref: '#/components/schemas/Ticket'

/api/product.serviceops/queue/{queue_guid}/associate-ticket-type/{ticket_type_guid}:
get:
tags: - Service Operations
summary: Associate ticket type to queue
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: queue_guid
in: path
required: true
schema:
type: string
format: uuid - name: ticket_type_guid
in: path
required: true
schema:
type: string
format: uuid
responses:
'200':
description: Association successful
content:
application/json:
schema:
type: object
properties:
status:
type: integer
message:
type: string

/api/product.serviceops/queue/{queue_guid}/disassociate-ticket-type/{ticket_type_guid}:
get:
tags: - Service Operations
summary: Disassociate ticket type from queue
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: queue_guid
in: path
required: true
schema:
type: string
format: uuid - name: ticket_type_guid
in: path
required: true
schema:
type: string
format: uuid
responses:
'200':
description: Disassociation successful
content:
application/json:
schema:
type: object
properties:
status:
type: integer
message:
type: string

/api/product.serviceops/ticket:
post:
tags: - Service Operations
summary: Create new ticket
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
queue_guid:
type: string
format: uuid
ticket_type_guid:
type: string
format: uuid
summary:
type: string
description:
type: string
contact_guid:
type: string
format: uuid
assigned_to_guid:
type: string
format: uuid
responses:
'200':
description: Ticket created successfully
content:
application/json:
schema:
type: object
properties:
status:
type: integer
message:
type: string
Ticket:
type: string
format: uuid

/api/product.serviceops/tickettypes:
get:
tags: - Service Operations
summary: Get ticket types
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: eligibleForQueue
in: query
schema:
type: string
responses:
'200':
description: List of ticket types
content:
application/json:
schema:
type: object
additionalProperties:
type: string

# Timesheets API

/api/product.serviceops/timesheets/timelog:
post:
tags: - Timesheets
summary: Log time entry
description: |
Creates a new time log entry.

        **Required Role**: `ROLE_TIMESHEETS_USER` or higher

        Users can only create time entries for themselves unless they have `ROLE_TIMESHEETS_EDITOR` or higher.
      parameters:
        - $ref: '#/components/parameters/ContentTypeJson'
        - $ref: '#/components/parameters/AcceptJson'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: string
                  format: uuid
                  description: User UUID (must be current user unless you have ROLE_TIMESHEETS_EDITOR)
                account:
                  type: string
                  description: Account identifier
                worktype:
                  type: integer
                  description: Work type ID
                time:
                  type: number
                  format: decimal
                  description: Time in hours
                description:
                  type: string
                  description: Description of the work performed
                entry_date:
                  type: string
                  format: date
                  description: Date of the time entry
                start_time:
                  type: string
                  format: time
                  description: Start time
                end_time:
                  type: string
                  format: time
                  description: End time
                geo_lat:
                  type: number
                  format: decimal
                  description: Latitude for location tracking
                geo_long:
                  type: number
                  format: decimal
                  description: Longitude for location tracking
      responses:
        '200':
          description: Time logged successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: integer
                  id:
                    type: integer
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'

/api/product.serviceops/timesheets/timelog/{id}/close:
get:
tags: - Timesheets
summary: Close time log
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: id
in: path
required: true
schema:
type: integer
responses:
'200':
description: Time log closed successfully
content:
application/json:
schema:
type: object
properties:
result:
type: integer
message:
type: string

/api/product.serviceops/timesheets/timelog/{id}/delete:
get:
tags: - Timesheets
summary: Delete time log
description: |
Deletes a time log entry.

        **Required Role**: `ROLE_TIMESHEETS_USER` or higher

        Users can only delete their own time entries unless they have `ROLE_TIMESHEETS_EDITOR` or higher.
      parameters:
        - $ref: '#/components/parameters/ContentTypeJson'
        - $ref: '#/components/parameters/AcceptJson'
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Time log entry ID
      responses:
        '200':
          description: Time log deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: integer
                  message:
                    type: string
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'

/api/product.serviceops/timesheets/timelog/opentimer:
get:
tags: - Timesheets
summary: Get open timers
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: Open timer details
content:
application/json:
schema:
type: object
properties:
result:
type: integer
timer:
$ref: '#/components/schemas/TimesheetEntry'

/api/product.serviceops/timesheets/timelog/list:
get:
tags: - Timesheets
summary: List time logs
description: |
Retrieves a list of time log entries with optional filtering.

        **Required Role**: `ROLE_TIMESHEETS_EDITOR` or higher

        This endpoint requires elevated permissions to view time logs across all users.
      parameters:
        - $ref: '#/components/parameters/ContentTypeJson'
        - $ref: '#/components/parameters/AcceptJson'
        - name: user
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by specific user UUID
        - name: from_date
          in: query
          schema:
            type: string
            format: date
          description: Start date for filtering entries
        - name: to_date
          in: query
          schema:
            type: string
            format: date
          description: End date for filtering entries
        - name: account
          in: query
          schema:
            type: string
          description: Filter by account identifier
        - name: worktype
          in: query
          schema:
            type: integer
          description: Filter by work type ID
        - name: page
          in: query
          schema:
            type: integer
            default: 1
          description: Page number for pagination
        - name: display
          in: query
          schema:
            type: integer
            default: 25
          description: Number of entries per page
      responses:
        '200':
          description: List of time entries
          content:
            application/json:
              schema:
                type: object
                properties:
                  entries:
                    type: array
                    items:
                      $ref: '#/components/schemas/TimesheetEntry'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'

/api/product.serviceops/timesheets/timelog/{id}:
get:
tags: - Timesheets
summary: Get specific time log
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: id
in: path
required: true
schema:
type: integer
responses:
'200':
description: Time log details
content:
application/json:
schema:
type: object
properties:
timelog:
$ref: '#/components/schemas/TimesheetEntry'
timesheet_users:
type: array
items:
$ref: '#/components/schemas/User'
post:
tags: - Timesheets
summary: Update time log
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: id
in: path
required: true
schema:
type: integer
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
user:
type: string
format: uuid
account:
type: string
worktype:
type: integer
time:
type: number
format: decimal
description:
type: string
entry_date:
type: string
format: date
start_time:
type: string
format: time
end_time:
type: string
format: time
responses:
'200':
description: Time log updated successfully
content:
application/json:
schema:
type: object
properties:
result:
type: integer

/api/product.serviceops/timesheets/accounts:
get:
tags: - Timesheets
summary: Get timesheet accounts
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: List of timesheet accounts
content:
application/json:
schema:
type: object
additionalProperties:
type: string

/api/product.serviceops/timesheets/worktypes:
get:
tags: - Timesheets
summary: Get work types
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: List of work types
content:
application/json:
schema:
type: object
additionalProperties:
type: string

/api/product.serviceops/timesheets/users:
get:
tags: - Timesheets
summary: Get timesheet users
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: List of timesheet users
content:
application/json:
schema:
type: object
additionalProperties:
type: string

# Privacy Operations

/api/service.privacyops/quickfilter/new:
post:
tags: - Privacy Operations
summary: Create quick filter
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
Shared:
type: boolean
Name:
type: string
ControllerName:
type: string
FilterSelected:
type: array
items:
type: string
Search:
type: string
responses:
'200':
description: Quick filter created
content:
application/json:
schema:
type: object
properties:
id:
type: integer

/api/service.privacyops/quickfilter/rename:
post:
tags: - Privacy Operations
summary: Rename quick filter
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
Id:
type: integer
Name:
type: string
responses:
'200':
description: Quick filter renamed
content:
application/json:
schema:
type: object
properties:
success:
type: integer

/api/service.privacyops/quickfilter/{id}/delete:
get:
tags: - Privacy Operations
summary: Delete quick filter
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: id
in: path
required: true
schema:
type: integer
responses:
'200':
description: Quick filter deleted
content:
application/json:
schema:
type: object
properties:
success:
type: integer

/api/service.privacyops/quickfilter/update:
post:
tags: - Privacy Operations
summary: Update quick filter
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
Id:
type: integer
FilterSelected:
type: array
items:
type: string
Search:
type: string
responses:
'200':
description: Quick filter updated
content:
application/json:
schema:
type: object
properties:
success:
type: integer

# Workflow Operations

/api/product.workflowops/workflows/execute/{guid}:
post:
tags: - Workflow Operations
summary: Execute workflow
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: guid
in: path
required: true
schema:
type: string
format: uuid
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
parameters:
type: object
additionalProperties: true
responses:
'200':
description: Workflow execution result
content:
application/json:
schema:
type: object
properties:
status:
type: integer
message:
type: string
requiredParameters:
type: array
items:
type: string

/api/product.workflowops/state-workflows:
get:
tags: - Workflow Operations
summary: Get state workflows
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: List of state workflows
content:
application/json:
schema:
type: object
additionalProperties:
type: string

# External Share

/api/product.privacyops/link:
post:
tags: - External Share
summary: Create external share link
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
source_type:
type: string
source_guid:
type: string
format: uuid
send_to:
type: string
format: email
message:
type: string
expiry:
type: string
format: date-time
allow_editing:
type: boolean
responses:
'200':
description: External share link created
content:
application/json:
schema:
type: object
properties:
status:
type: integer
message:
type: string
link:
type: string
format: uri

# File Operations

/api/files/new:
post:
tags: - File Operations
summary: Upload new file
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
requestBody:
required: true
content:
multipart/form-data:
schema:
type: object
properties:
collection_guid:
type: string
format: uuid
path:
type: string
expiry:
type: string
format: date-time
file:
type: string
format: binary
responses:
'200':
description: File uploaded successfully
content:
application/json:
schema:
type: object
properties:
status:
type: integer
message:
type: string
file_guid:
type: string
format: uuid

# Licensing

/api/licensing/check:
post:
tags: - Licensing
summary: Check license validity
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
LICENSE_KEY:
type: string
responses:
'200':
description: License validity check result
content:
application/json:
schema:
type: object
properties:
Expiry:
type: string
format: date-time
Valid:
type: boolean

# Account Management

/api/account/whoami:
get:
tags: - Account Management
summary: Get current user information
description: Returns information about the currently authenticated user
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
responses:
'200':
description: Current user information
content:
application/json:
schema:
type: object
properties:
Guid:
type: string
format: uuid
FirstName:
type: string
LastName:
type: string
EmailAddress:
type: string
format: email
Entity:
type: string
format: uuid
EntityName:
type: string
EntityShortcode:
type: string
MobilePhone:
type: string
AccountEnabled:
type: boolean
MFAEnabled:
type: boolean
'401':
$ref: '#/components/responses/UnauthorizedError'

/api/account/logout:
get:
tags: - Account Management
summary: Logout user
description: Logout the current user and invalidate API token if provided
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson' - name: X-AUTH-TOKEN
in: header
schema:
type: string
format: uuid
description: API token to invalidate
responses:
'200':
description: Logout successful
content:
application/json:
schema:
type: object
properties:
result:
type: integer
example: 0
'401':
$ref: '#/components/responses/UnauthorizedError'

/api/getentitybranchserver:
post:
tags: - Account Management
summary: Get entity branch server
description: Get branch server information for an entity
parameters: - $ref: '#/components/parameters/ContentTypeJson' - $ref: '#/components/parameters/AcceptJson'
requestBody:
required: true
content:
application/json:
schema:
type: object
properties:
entity_code:
type: string
description: Entity code to lookup
responses:
'200':
description: Branch server information
content:
application/json:
schema:
type: object
description: Branch server details (currently returns empty object)

/unprivileged/api/account/applogin:
post:
tags: - Authentication
summary: Create API Token
description: |
Creates an API token for authentication. This endpoint does not require authentication.

        **Authentication Flow:**
        1. Call this endpoint with entity_code, email_address, and password
        2. Receive an API token in the response
        3. Use the token in the `x-auth-token` header for all subsequent API calls

        **Multi-Factor Authentication:**
        If MFA is enabled for the user, the first call may return a result code indicating MFA is required.
        In this case, call the endpoint again with the same credentials plus the `mfa_token` parameter.

        **Important:** This endpoint requires `Content-Type: application/json` header.
      security: []
      parameters:
        - $ref: '#/components/parameters/ContentTypeJson'
        - $ref: '#/components/parameters/AcceptJson'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                entity_code:
                  type: string
                  description: Entity code for login
                email_address:
                  type: string
                  format: email
                  description: User email address
                password:
                  type: string
                  description: User password
                mfa_token:
                  type: string
                  description: Multi-factor authentication token (if required)
              required:
                - entity_code
                - email_address
                - password
      responses:
        '200':
          description: Login successful - API token created
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: integer
                    description: Result code (0 = success)
                    example: 0
                  message:
                    type: string
                    description: Result message
                    example: "Login successful"
                  token:
                    type: string
                    format: uuid
                    description: |
                      API authentication token. Use this token in the `x-auth-token` header
                      for all subsequent API requests.
                    example: "550e8400-e29b-41d4-a716-************"
                  user:
                    $ref: '#/components/schemas/User'
                  entity:
                    $ref: '#/components/schemas/Entity'
        '401':
          description: Login failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: integer
                    example: 1
                  message:
                    type: string
                    example: "Invalid credentials"
        '422':
          description: MFA required
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: integer
                    example: 2
                  message:
                    type: string
                    example: "MFA token required"

components:
schemas:
Account:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
type:
type: integer
code:
type: string
description:
type: string
currency:
type: string
parent:
$ref: '#/components/schemas/Account'
sub_accounts:
type: array
items:
$ref: '#/components/schemas/Account'
readOnly: true
budgets:
type: array
items:
$ref: '#/components/schemas/Budget'
readOnly: true
roles:
type: array
items:
type: string
ApiKeys:
type: object
properties:
id:
type: integer
readOnly: true
user:
$ref: '#/components/schemas/User'
token:
type: string
format: uuid
description:
type: string
expiry:
type: string
format: date-time
enabled:
type: boolean
Approver:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
user:
$ref: '#/components/schemas/User'
type:
type: string
lower_limit:
type: number
format: decimal
upper_limit:
type: number
format: decimal
other_limit:
type: array
items:
type: string
description:
type: string
Article:
type: object
properties:
id:
type: integer
readOnly: true
topic:
$ref: '#/components/schemas/Topic'
title:
type: string
body:
type: string
tags:
type: array
items:
type: string
published_at:
type: string
format: date-time
updated_at:
type: string
format: date-time
author:
$ref: '#/components/schemas/User'
BackgroundJob:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
user:
$ref: '#/components/schemas/User'
job_key:
type: string
priority:
type: integer
parameters:
type: array
items:
type: string
status:
type: integer
requested_at:
type: string
format: date-time
processed_at:
type: string
format: date-time
completed_at:
type: string
format: date-time
outcome:
type: integer
job_guid:
type: string
format: uuid
background_job_transient_token:
type: string
result:
type: array
items:
type: string
Branch:
type: object
properties:
id:
type: integer
readOnly: true
name:
type: string
secret:
type: string
location:
type: string
expiry:
type: string
format: date-time
remote_address:
type: string
accepting:
type: boolean
connection_status:
type: integer
last_connected_to_at:
type: string
format: date-time
last_connected_from_at:
type: string
format: date-time
entity_count:
type: integer
Budget:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
account:
$ref: '#/components/schemas/Account'
description:
type: string
financial_year:
type: integer
amount:
type: number
format: decimal
is_expense_budget:
type: boolean
Call:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
agent:
$ref: '#/components/schemas/User'
call_from:
type: string
call_to:
type: string
contact:
$ref: '#/components/schemas/Customer'
call_at:
type: string
format: date-time
direction:
type: integer
subject:
type: string
notes:
type: string
guid:
type: string
format: uuid
call_completed:
type: boolean
spoke_to:
type: string
CreditPack:
type: object
properties:
id:
type: integer
readOnly: true
guid:
type: string
format: uuid
created_at:
type: string
format: date-time
activated_at:
type: string
format: date-time
use_by:
type: string
format: date-time
activated_by_entity:
$ref: '#/components/schemas/Entity'
credits:
type: integer
pin:
type: string
CustomData:
type: object
properties:
id:
type: integer
readOnly: true
name:
type: string
extends:
type: string
extends_conditions:
type: array
items:
type: string
entity:
$ref: '#/components/schemas/Entity'
CustomDataField:
type: object
properties:
id:
type: integer
readOnly: true
custom_data:
$ref: '#/components/schemas/CustomData'
name:
type: string
type:
type: string
CustomDataRecord:
type: object
properties:
id:
type: integer
readOnly: true
guid:
type: string
format: uuid
entity:
$ref: '#/components/schemas/Entity'
custom_data:
$ref: '#/components/schemas/CustomData'
created_at:
type: string
format: date-time
created_by:
$ref: '#/components/schemas/User'
CustomDataRecordData:
type: object
properties:
id:
type: integer
readOnly: true
custom_data_record:
$ref: '#/components/schemas/CustomDataRecord'
custom_data_field:
$ref: '#/components/schemas/CustomDataField'
value:
type: string
custom_data:
$ref: '#/components/schemas/CustomData'
entity:
$ref: '#/components/schemas/Entity'
Customer:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
account_name:
type: string
first_name:
type: string
last_name:
type: string
account_code:
type: string
account_manager:
$ref: '#/components/schemas/User'
created_by:
$ref: '#/components/schemas/User'
created_at:
type: string
format: date-time
account_type:
type: integer
industry:
type: integer
mobile_phone:
type: string
main_phone:
type: string
email_address:
type: string
billing_email:
type: string
website:
type: string
billing_address_1:
type: string
billing_address_2:
type: string
billing_address_city_town:
type: string
billing_address_region:
type: string
billing_address_country:
type: string
shipping_address_1:
type: string
shipping_address_2:
type: string
shipping_address_city_town:
type: string
shipping_address_postcode:
type: string
shipping_address_region:
type: string
shipping_address_country:
type: string
parent:
$ref: '#/components/schemas/Customer'
sub_accounts:
type: array
items:
$ref: '#/components/schemas/Customer'
readOnly: true
guid:
type: string
format: uuid
status:
$ref: '#/components/schemas/DocumentState'
contact_type:
$ref: '#/components/schemas/CustomerType'
customer_entity_branch_server:
$ref: '#/components/schemas/Branch'
customer_entity_shortcode:
type: string
government_identifier:
type: string
customer_entity_configuration:
type: array
items:
type: string
country_of_registration:
type: string
linked_user:
$ref: '#/components/schemas/User'
is_lead:
type: boolean
lead_source:
type: integer
job_title:
type: string
number_of_employees:
type: string
CustomerType:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
state_workflow:
$ref: '#/components/schemas/DocumentStateWorkflow'
start_state:
$ref: '#/components/schemas/DocumentState'
Deal:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
deal_number:
type: string
name:
type: string
contact:
$ref: '#/components/schemas/Customer'
deal_owner:
$ref: '#/components/schemas/User'
description:
type: string
closing_date:
type: string
format: date
value:
type: number
format: decimal
quotes:
type: array
items:
$ref: '#/components/schemas/Quote'
readOnly: true
created_at:
type: string
format: date-time
guid:
type: string
format: uuid
status:
$ref: '#/components/schemas/DocumentState'
Document:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
number:
type: string
ledger_entries:
type: array
items:
$ref: '#/components/schemas/Ledger'
readOnly: true
current_state:
$ref: '#/components/schemas/DocumentState'
content:
type: string
owner:
$ref: '#/components/schemas/User'
permissions:
type: array
items:
type: string
version_date:
type: string
format: date-time
deleted:
type: boolean
name:
type: string
guid:
type: string
format: uuid
template:
$ref: '#/components/schemas/Template'
locked_by:
$ref: '#/components/schemas/User'
last_edited:
type: string
format: date-time
sync_hash:
type: array
items:
type: string
DocumentState:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
state_workflow:
$ref: '#/components/schemas/DocumentStateWorkflow'
name:
type: string
type:
type: integer
guid:
type: string
format: uuid
DocumentStateTransition:
type: object
properties:
id:
type: integer
readOnly: true
name:
type: string
from_state:
$ref: '#/components/schemas/DocumentState'
to_state:
$ref: '#/components/schemas/DocumentState'
workflow:
$ref: '#/components/schemas/DocumentStateWorkflow'
guid:
type: string
format: uuid
require_comment:
type: boolean
require_field_validation:
$ref: '#/components/schemas/FieldValidation'
execute_workflow:
$ref: '#/components/schemas/Workflow'
DocumentStateTransitionPermission:
type: object
properties:
id:
type: integer
readOnly: true
document_state_transition:
$ref: '#/components/schemas/DocumentStateTransition'
user:
$ref: '#/components/schemas/User'
in_group:
$ref: '#/components/schemas/Group'
role:
type: string
deny:
type: boolean
DocumentStateWorkflow:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
guid:
type: string
format: uuid
Entity:
type: object
properties:
id:
type: integer
readOnly: true
name:
type: string
shortcode:
type: string
address_line_1:
type: string
address_line_2:
type: string
town_suburb:
type: string
city_district:
type: string
postcode:
type: string
phone_number:
type: string
email_address:
type: string
website:
type: string
guid:
type: string
format: uuid
FieldValidation:
type: object
properties:
id:
type: integer
readOnly: true
name:
type: string
entity:
$ref: '#/components/schemas/Entity'
Group:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
description:
type: string
guid:
type: string
format: uuid
Ledger:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
account:
$ref: '#/components/schemas/Account'
amount:
type: number
format: decimal
description:
type: string
transaction_date:
type: string
format: date-time
Meeting:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
user:
$ref: '#/components/schemas/User'
name:
type: string
internal_attendees:
type: array
items:
$ref: '#/components/schemas/User'
external_attendees:
type: array
items:
$ref: '#/components/schemas/Customer'
meeting_at:
type: string
format: date-time
agenda:
type: string
notes:
type: string
meeting_location:
type: string
virtual_meeting_link:
type: string
meeting_duration:
type: integer
contact:
$ref: '#/components/schemas/Customer'
guid:
type: string
format: uuid
Queue:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
shortcode:
type: string
description:
type: string
active:
type: boolean
guid:
type: string
format: uuid
Quote:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
quote_number:
type: string
customer:
$ref: '#/components/schemas/Customer'
deal:
$ref: '#/components/schemas/Deal'
total_amount:
type: number
format: decimal
created_at:
type: string
format: date-time
guid:
type: string
format: uuid
Template:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
content:
type: string
guid:
type: string
format: uuid
Ticket:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
ticket_type:
$ref: '#/components/schemas/TicketType'
queue:
$ref: '#/components/schemas/Queue'
summary:
type: string
description:
type: string
created_by:
$ref: '#/components/schemas/User'
assigned_to:
$ref: '#/components/schemas/User'
created_at:
type: string
format: date-time
status:
$ref: '#/components/schemas/DocumentState'
contact:
$ref: '#/components/schemas/Customer'
parent:
$ref: '#/components/schemas/Ticket'
timesheet_account:
$ref: '#/components/schemas/TimesheetAccount'
labels:
type: array
items:
type: string
rank:
type: integer
guid:
type: string
format: uuid
ticket_number:
type: string
related_tickets:
type: array
items:
$ref: '#/components/schemas/Ticket'
TicketType:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
description:
type: string
guid:
type: string
format: uuid
TimesheetAccount:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
code:
type: string
enabled:
type: boolean
guid:
type: string
format: uuid
TimesheetEntry:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
user:
$ref: '#/components/schemas/User'
entered_by:
$ref: '#/components/schemas/User'
timesheet_account:
$ref: '#/components/schemas/TimesheetAccount'
type:
$ref: '#/components/schemas/TimesheetType'
time:
type: number
format: decimal
start_time:
type: string
format: time
end_time:
type: string
format: time
entry_date:
type: string
format: date
description:
type: string
logged:
type: boolean
geo_lat:
type: number
format: decimal
geo_long:
type: number
format: decimal
TimesheetType:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
enabled:
type: boolean
guid:
type: string
format: uuid
Topic:
type: object
properties:
id:
type: integer
readOnly: true
name:
type: string
description:
type: string
User:
type: object
properties:
id:
type: integer
readOnly: true
email_address:
type: string
format: email
first_name:
type: string
last_name:
type: string
entity:
type: array
items:
$ref: '#/components/schemas/Entity'
current_entity:
$ref: '#/components/schemas/Entity'
manager:
$ref: '#/components/schemas/User'
direct_reports:
type: array
items:
$ref: '#/components/schemas/User'
groups:
type: array
items:
$ref: '#/components/schemas/Group'
guid:
type: string
format: uuid
account_enabled:
type: boolean
mobile_phone:
type: string
mfa_enabled:
type: boolean
Workflow:
type: object
properties:
id:
type: integer
readOnly: true
entity:
$ref: '#/components/schemas/Entity'
name:
type: string
description:
type: string
parameters:
type: object
additionalProperties: true
guid:
type: string
format: uuid
securitySchemes:
apiKeyAuth:
type: apiKey
in: header
name: x-auth-token
description: API key authentication using x-auth-token header
parameters:
ContentTypeJson:
name: Content-Type
in: header
required: true
schema:
type: string
default: application/json
description: Content type must be application/json
AcceptJson:
name: Accept
in: header
required: true
schema:
type: string
default: application/json
description: Accept header must be application/json
responses:
UnauthorizedError:
description: Authentication information is missing or invalid
content:
application/json:
schema:
type: object
properties:
status:
type: integer
example: 401
message:
type: string
example: "Unauthorized access"
ForbiddenError:
description: Access forbidden - insufficient permissions
content:
application/json:
schema:
type: object
properties:
status:
type: integer
example: 403
message:
type: string
example: "Access forbidden"
NotFoundError:
description: Resource not found
content:
application/json:
schema:
type: object
properties:
status:
type: integer
example: 404
message:
type: string
example: "Resource not found"
ValidationError:
description: Validation error
content:
application/json:
schema:
type: object
properties:
status:
type: integer
example: 422
message:
type: string
example: "Validation failed"
errors:
type: object
additionalProperties:
type: array
items:
type: string
ServerError:
description: Internal server error
content:
application/json:
schema:
type: object
properties:
status:
type: integer
example: 500
message:
type: string
example: "Internal server error"
security:

- apiKeyAuth: []