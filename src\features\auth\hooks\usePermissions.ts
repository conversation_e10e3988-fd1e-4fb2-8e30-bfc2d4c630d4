import { useAuth } from './useAuth';
import { Permission, PERMISSIONS } from '../schemas/authSchemas';

export const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = (permission: Permission): boolean => {
    if (!user || !user.permissions) return false;
    return user.permissions.includes(permission);
  };

  const hasAnyPermission = (permissions: Permission[]): boolean => {
    if (!user || !user.permissions) return false;
    return permissions.some(permission => user.permissions.includes(permission));
  };

  const hasAllPermissions = (permissions: Permission[]): boolean => {
    if (!user || !user.permissions) return false;
    return permissions.every(permission => user.permissions.includes(permission));
  };

  const hasRole = (role: string): boolean => {
    if (!user || !user.roles) return false;
    return user.roles.includes(role);
  };

  const isEntityAdmin = (): boolean => {
    return hasPermission(PERMISSIONS['system:admin']);
  };

  const canAccessModule = (module: string): boolean => {
    switch (module) {
      case 'customerops':
        return hasPermission(PERMISSIONS['customerops:read']);
      case 'serviceops':
        return hasPermission(PERMISSIONS['serviceops:read']);
      case 'timesheets':
        return hasPermission(PERMISSIONS['timesheets:read']);
      case 'accountops':
        return hasPermission(PERMISSIONS['accountops:read']);
      case 'system':
        return hasPermission(PERMISSIONS['system:admin']);
      default:
        return false;
    }
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    isEntityAdmin,
    canAccessModule,
    userPermissions: user?.permissions || [],
    userRoles: user?.roles || [],
  };
};