import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal } from 'lucide-react';

interface AuthAlertProps {
  isMfaRequired: boolean;
  error?: Error | null;
}

export const AuthAlert = ({ isMfaRequired, error }: AuthAlertProps) => {
  if (!error) return null;

  return (
    <Alert variant='destructive' className='mb-4'>
      <Terminal className='h-4 w-4' />
      <AlertTitle>{isMfaRequired ? 'MFA Failed' : 'Login Failed'}</AlertTitle>
      <AlertDescription>
        {error.message || 'An unknown error occurred.'}
      </AlertDescription>
    </Alert>
  );
};
