import { useState } from 'react';
import { 
  LayoutGrid, 
  List, 
  Plus, 
  Ticket,
  Clock,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { useTicketStats } from '../hooks/useTickets';
import { Ticket as TicketType } from '../schemas/ticketSchemas';

import { TicketList } from './TicketList';
import { TicketBoard } from './TicketBoard';

export function TicketManagement() {
  const [selectedTicket, setSelectedTicket] = useState<TicketType | null>(null);
  const [showTicketForm, setShowTicketForm] = useState(false);
  const [showAssignmentForm, setShowAssignmentForm] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'board'>('list');

  const { data: ticketStats } = useTicketStats();

  const handleCreateTicket = () => {
    setSelectedTicket(null);
    setShowTicketForm(true);
  };

  const handleEditTicket = (ticket: TicketType) => {
    setSelectedTicket(ticket);
    setShowTicketForm(true);
  };

  const handleViewTicket = (ticket: TicketType) => {
    setSelectedTicket(ticket);
    // Could implement a detailed view dialog here
  };

  const handleAssignTicket = (ticket: TicketType) => {
    setSelectedTicket(ticket);
    setShowAssignmentForm(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Ticket Management</h1>
          <p className="text-lg text-muted-foreground">
            Manage support tickets and service requests
          </p>
        </div>
        <div className="flex gap-2">
          <div className="flex rounded-lg border bg-background p-1">
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="mr-2 h-4 w-4" />
              List
            </Button>
            <Button
              variant={viewMode === 'board' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('board')}
            >
              <LayoutGrid className="mr-2 h-4 w-4" />
              Board
            </Button>
          </div>
          <RoleGuard permissions={[PERMISSIONS['serviceops:write']]}>
            <Button onClick={handleCreateTicket}>
              <Plus className="mr-2 h-4 w-4" />
              Create Ticket
            </Button>
          </RoleGuard>
        </div>
      </div>

      {/* Ticket Overview Stats */}
      {ticketStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Ticket className="h-4 w-4" />
                Total Tickets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{ticketStats.totalTickets}</div>
              <p className="text-xs text-muted-foreground">
                {ticketStats.openTickets} open, {ticketStats.inProgressTickets} in progress
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Resolved Today
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{ticketStats.resolvedToday}</div>
              <p className="text-xs text-muted-foreground">
                Avg: {ticketStats.avgResolutionTime.toFixed(1)}h resolution
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Response Time
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{ticketStats.avgResponseTime.toFixed(0)}m</div>
              <p className="text-xs text-muted-foreground">Average first response</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                SLA Breaches
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{ticketStats.slaBreaches}</div>
              <p className="text-xs text-muted-foreground">
                {ticketStats.slaPerformance.responseTimeCompliance.toFixed(1)}% SLA compliance
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'list' | 'board')}>
        <TabsList className="hidden">
          <TabsTrigger value="list">List</TabsTrigger>
          <TabsTrigger value="board">Board</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <TicketList
            onCreateTicket={handleCreateTicket}
            onEditTicket={handleEditTicket}
            onViewTicket={handleViewTicket}
            onAssignTicket={handleAssignTicket}
          />
        </TabsContent>

        <TabsContent value="board" className="space-y-4">
          <TicketBoard
            onCreateTicket={handleCreateTicket}
            onEditTicket={handleEditTicket}
            onViewTicket={handleViewTicket}
          />
        </TabsContent>
      </Tabs>

      {/* Ticket Form Dialog - Placeholder */}
      {showTicketForm && (
        <Dialog open={showTicketForm} onOpenChange={setShowTicketForm}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>
                {selectedTicket ? 'Edit Ticket' : 'Create Ticket'}
              </DialogTitle>
            </DialogHeader>
            <div className="p-4">
              <p className="text-muted-foreground mb-4">
                Ticket form component would go here. This would include fields for:
                subject, description, category, priority, assignment, etc.
              </p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowTicketForm(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowTicketForm(false)}>
                  Save Ticket
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Assignment Form Dialog - Placeholder */}
      {showAssignmentForm && (
        <Dialog open={showAssignmentForm} onOpenChange={setShowAssignmentForm}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Assign Ticket</DialogTitle>
            </DialogHeader>
            <div className="p-4">
              <p className="text-muted-foreground mb-4">
                Ticket assignment form would go here. Assign "{selectedTicket?.subject}" to a user or group.
              </p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowAssignmentForm(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowAssignmentForm(false)}>
                  Assign Ticket
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}