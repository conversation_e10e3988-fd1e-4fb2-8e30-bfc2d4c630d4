import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import {
  Timesheet,
  TimeEntry,
  TimerSession,
  ApprovalWorkflow,
  ProjectReference,
  TaskReference,
  TimesheetFormValues,
  TimeEntryFormValues,
  TimesheetSearchParams,
  TimesheetListResponse,
  TimeEntryListResponse,
  TimesheetStats,
  timesheetListResponseSchema,
  timeEntryListResponseSchema,
  timesheetSchema,
  timeEntrySchema,
  timerSessionSchema,
  approvalWorkflowSchema,
  projectReferenceSchema,
  taskReferenceSchema,
  timesheetStatsSchema,
} from '../schemas/timesheetSchemas';

const TIMESHEETS_QUERY_KEY = 'timesheets';
const TIME_ENTRIES_QUERY_KEY = 'time-entries';
const TIMER_SESSIONS_QUERY_KEY = 'timer-sessions';
const APPROVAL_WORKFLOWS_QUERY_KEY = 'approval-workflows';
const PROJECTS_QUERY_KEY = 'projects';
const TASKS_QUERY_KEY = 'tasks';

// Timesheet Query hooks
export const useTimesheets = (searchParams?: TimesheetSearchParams) => {
  return useQuery({
    queryKey: [TIMESHEETS_QUERY_KEY, searchParams],
    queryFn: async (): Promise<TimesheetListResponse> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await apiClient.request<TimesheetListResponse>(
        `/api/product.serviceops/timesheets?${params.toString()}`
      );
      
      return timesheetListResponseSchema.parse(response);
    },
  });
};

export const useTimesheet = (timesheetId: string) => {
  return useQuery({
    queryKey: [TIMESHEETS_QUERY_KEY, timesheetId],
    queryFn: async (): Promise<Timesheet> => {
      const response = await apiClient.request<Timesheet>(
        `/api/product.serviceops/timesheets/${timesheetId}`
      );
      return timesheetSchema.parse(response);
    },
    enabled: !!timesheetId,
  });
};

export const useTimesheetStats = (filters?: Partial<TimesheetSearchParams>) => {
  return useQuery({
    queryKey: [TIMESHEETS_QUERY_KEY, 'stats', filters],
    queryFn: async (): Promise<TimesheetStats> => {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });
      }
      
      const response = await apiClient.request<TimesheetStats>(
        `/api/product.serviceops/timesheets/stats?${params.toString()}`
      );
      
      return timesheetStatsSchema.parse(response);
    },
  });
};

// Time Entry Query hooks
export const useTimeEntries = (searchParams?: TimesheetSearchParams) => {
  return useQuery({
    queryKey: [TIME_ENTRIES_QUERY_KEY, searchParams],
    queryFn: async (): Promise<TimeEntryListResponse> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await apiClient.request<TimeEntryListResponse>(
        `/api/product.serviceops/timesheets/timelog/list?${params.toString()}`
      );
      
      return timeEntryListResponseSchema.parse(response);
    },
  });
};

export const useTimeEntry = (entryId: string) => {
  return useQuery({
    queryKey: [TIME_ENTRIES_QUERY_KEY, entryId],
    queryFn: async (): Promise<TimeEntry> => {
      const response = await apiClient.request<TimeEntry>(
        `/api/product.serviceops/timesheets/timelog/${entryId}`
      );
      return timeEntrySchema.parse(response);
    },
    enabled: !!entryId,
  });
};

// Timer Session hooks
export const useTimerSessions = (userId?: string) => {
  return useQuery({
    queryKey: [TIMER_SESSIONS_QUERY_KEY, userId],
    queryFn: async (): Promise<TimerSession[]> => {
      const params = new URLSearchParams();
      if (userId) {
        params.append('userId', userId);
      }
      
      const response = await apiClient.request<TimerSession[]>(
        `/api/product.serviceops/timesheets/timer-sessions?${params.toString()}`
      );
      
      return response.map(session => timerSessionSchema.parse(session));
    },
  });
};

export const useActiveTimerSession = (userId: string) => {
  return useQuery({
    queryKey: [TIMER_SESSIONS_QUERY_KEY, 'active', userId],
    queryFn: async (): Promise<TimerSession | null> => {
      const response = await apiClient.request<TimerSession | null>(
        `/api/product.serviceops/timesheets/timelog/opentimer`
      );
      
      return response ? timerSessionSchema.parse(response) : null;
    },
    enabled: !!userId,
    refetchInterval: 30000, // Refetch every 30 seconds to keep timer updated
  });
};

// Project and Task reference hooks
export const useProjects = (entityId?: string) => {
  return useQuery({
    queryKey: [PROJECTS_QUERY_KEY, entityId],
    queryFn: async (): Promise<ProjectReference[]> => {
      const params = new URLSearchParams();
      if (entityId) {
        params.append('entityId', entityId);
      }
      
      const response = await apiClient.request<ProjectReference[]>(
        `/api/product.serviceops/timesheets/accounts?${params.toString()}`
      );
      
      return response.map(project => projectReferenceSchema.parse(project));
    },
  });
};

export const useTasks = (projectId?: string) => {
  return useQuery({
    queryKey: [TASKS_QUERY_KEY, projectId],
    queryFn: async (): Promise<TaskReference[]> => {
      const params = new URLSearchParams();
      if (projectId) {
        params.append('projectId', projectId);
      }
      
      const response = await apiClient.request<TaskReference[]>(
        `/api/product.serviceops/timesheets/worktypes?${params.toString()}`
      );
      
      return response.map(task => taskReferenceSchema.parse(task));
    },
    enabled: !!projectId,
  });
};

// Timesheet Mutation hooks
export const useCreateTimesheet = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (timesheet: TimesheetFormValues): Promise<Timesheet> => {
      const response = await apiClient.request<Timesheet>('/api/product.serviceops/timesheets', {
        method: 'POST',
        body: JSON.stringify(timesheet),
      });
      return timesheetSchema.parse(response);
    },
    onSuccess: (newTimesheet) => {
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
      queryClient.setQueryData([TIMESHEETS_QUERY_KEY, newTimesheet.id], newTimesheet);
    },
  });
};

export const useUpdateTimesheet = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      timesheetId, 
      timesheet 
    }: { 
      timesheetId: string; 
      timesheet: Partial<TimesheetFormValues> 
    }): Promise<Timesheet> => {
      const response = await apiClient.request<Timesheet>(`/api/product.serviceops/timesheets/${timesheetId}`, {
        method: 'PUT',
        body: JSON.stringify(timesheet),
      });
      return timesheetSchema.parse(response);
    },
    onSuccess: (updatedTimesheet) => {
      queryClient.setQueryData([TIMESHEETS_QUERY_KEY, updatedTimesheet.id], updatedTimesheet);
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
    },
  });
};

export const useDeleteTimesheet = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (timesheetId: string): Promise<void> => {
      await apiClient.request(`/api/product.serviceops/timesheets/${timesheetId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: (_, timesheetId) => {
      queryClient.removeQueries({ queryKey: [TIMESHEETS_QUERY_KEY, timesheetId] });
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
    },
  });
};

// Time Entry Mutation hooks
export const useCreateTimeEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (timeEntry: TimeEntryFormValues): Promise<TimeEntry> => {
      const response = await apiClient.request<TimeEntry>('/api/product.serviceops/timesheets/timelog', {
        method: 'POST',
        body: JSON.stringify(timeEntry),
      });
      return timeEntrySchema.parse(response);
    },
    onSuccess: (newEntry) => {
      queryClient.invalidateQueries({ queryKey: [TIME_ENTRIES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
      queryClient.setQueryData([TIME_ENTRIES_QUERY_KEY, newEntry.id], newEntry);
    },
  });
};

export const useUpdateTimeEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      entryId, 
      timeEntry 
    }: { 
      entryId: string; 
      timeEntry: Partial<TimeEntryFormValues> 
    }): Promise<TimeEntry> => {
      const response = await apiClient.request<TimeEntry>(`/api/product.serviceops/timesheets/timelog/${entryId}`, {
        method: 'POST',
        body: JSON.stringify(timeEntry),
      });
      return timeEntrySchema.parse(response);
    },
    onSuccess: (updatedEntry) => {
      queryClient.setQueryData([TIME_ENTRIES_QUERY_KEY, updatedEntry.id], updatedEntry);
      queryClient.invalidateQueries({ queryKey: [TIME_ENTRIES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
    },
  });
};

export const useDeleteTimeEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (entryId: string): Promise<void> => {
      await apiClient.request(`/api/product.serviceops/timesheets/timelog/${entryId}/delete`, {
        method: 'GET',
      });
    },
    onSuccess: (_, entryId) => {
      queryClient.removeQueries({ queryKey: [TIME_ENTRIES_QUERY_KEY, entryId] });
      queryClient.invalidateQueries({ queryKey: [TIME_ENTRIES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
    },
  });
};

// Timer Session Mutation hooks
export const useStartTimer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (timerData: {
      description: string;
      projectId?: string;
      taskId?: string;
      userId: string;
      entityId: string;
    }): Promise<TimerSession> => {
      const response = await apiClient.request<TimerSession>('/api/product.serviceops/timesheets/timelog', {
        method: 'POST',
        body: JSON.stringify(timerData),
      });
      return timerSessionSchema.parse(response);
    },
    onSuccess: (session) => {
      queryClient.invalidateQueries({ queryKey: [TIMER_SESSIONS_QUERY_KEY] });
      queryClient.setQueryData([TIMER_SESSIONS_QUERY_KEY, 'active', session.userId], session);
    },
  });
};

export const usePauseTimer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (sessionId: string): Promise<TimerSession> => {
      const response = await apiClient.request<TimerSession>(`/api/product.serviceops/timesheets/timelog/${sessionId}/pause`, {
        method: 'POST',
      });
      return timerSessionSchema.parse(response);
    },
    onSuccess: (session) => {
      queryClient.invalidateQueries({ queryKey: [TIMER_SESSIONS_QUERY_KEY] });
      queryClient.setQueryData([TIMER_SESSIONS_QUERY_KEY, 'active', session.userId], session);
    },
  });
};

export const useResumeTimer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (sessionId: string): Promise<TimerSession> => {
      const response = await apiClient.request<TimerSession>(`/api/product.serviceops/timesheets/timelog/${sessionId}/resume`, {
        method: 'POST',
      });
      return timerSessionSchema.parse(response);
    },
    onSuccess: (session) => {
      queryClient.invalidateQueries({ queryKey: [TIMER_SESSIONS_QUERY_KEY] });
      queryClient.setQueryData([TIMER_SESSIONS_QUERY_KEY, 'active', session.userId], session);
    },
  });
};

export const useStopTimer = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (sessionId: string): Promise<TimeEntry> => {
      const response = await apiClient.request<TimeEntry>(`/api/product.serviceops/timesheets/timelog/${sessionId}/close`, {
        method: 'GET',
      });
      return timeEntrySchema.parse(response);
    },
    onSuccess: (entry) => {
      queryClient.invalidateQueries({ queryKey: [TIMER_SESSIONS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [TIME_ENTRIES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
      queryClient.setQueryData([TIME_ENTRIES_QUERY_KEY, entry.id], entry);
      queryClient.setQueryData([TIMER_SESSIONS_QUERY_KEY, 'active', entry.userId], null);
    },
  });
};

// Approval hooks
export const useSubmitTimesheet = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (timesheetId: string): Promise<Timesheet> => {
      const response = await apiClient.request<Timesheet>(`/api/product.serviceops/timesheets/${timesheetId}/submit`, {
        method: 'POST',
      });
      return timesheetSchema.parse(response);
    },
    onSuccess: (timesheet) => {
      queryClient.setQueryData([TIMESHEETS_QUERY_KEY, timesheet.id], timesheet);
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
    },
  });
};

export const useApproveTimesheet = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      timesheetId, 
      comment 
    }: { 
      timesheetId: string; 
      comment?: string; 
    }): Promise<Timesheet> => {
      const response = await apiClient.request<Timesheet>(`/api/product.serviceops/timesheets/${timesheetId}/approve`, {
        method: 'POST',
        body: JSON.stringify({ comment }),
      });
      return timesheetSchema.parse(response);
    },
    onSuccess: (timesheet) => {
      queryClient.setQueryData([TIMESHEETS_QUERY_KEY, timesheet.id], timesheet);
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
    },
  });
};

export const useRejectTimesheet = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      timesheetId, 
      reason 
    }: { 
      timesheetId: string; 
      reason: string; 
    }): Promise<Timesheet> => {
      const response = await apiClient.request<Timesheet>(`/api/product.serviceops/timesheets/${timesheetId}/reject`, {
        method: 'POST',
        body: JSON.stringify({ reason }),
      });
      return timesheetSchema.parse(response);
    },
    onSuccess: (timesheet) => {
      queryClient.setQueryData([TIMESHEETS_QUERY_KEY, timesheet.id], timesheet);
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
    },
  });
};

// Bulk operations
export const useBulkUpdateTimeEntries = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      entryIds, 
      updates 
    }: { 
      entryIds: string[]; 
      updates: Partial<TimeEntryFormValues> 
    }): Promise<TimeEntry[]> => {
      const response = await apiClient.request<TimeEntry[]>('/api/product.serviceops/timesheets/timelog/bulk-update', {
        method: 'POST',
        body: JSON.stringify({ entryIds, updates }),
      });
      
      return response.map(entry => timeEntrySchema.parse(entry));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TIME_ENTRIES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [TIMESHEETS_QUERY_KEY] });
    },
  });
};

// Export functionality
export const useExportTimesheets = () => {
  return useMutation({
    mutationFn: async (searchParams?: TimesheetSearchParams): Promise<Blob> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await fetch(`/api/product.serviceops/timesheets/export?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-AUTH-TOKEN': localStorage.getItem('authToken') || '',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Export failed: ${response.status}`);
      }
      
      return response.blob();
    },
  });
};

// Approval Workflow hooks
export const useApprovalWorkflows = () => {
  return useQuery({
    queryKey: [APPROVAL_WORKFLOWS_QUERY_KEY],
    queryFn: async (): Promise<ApprovalWorkflow[]> => {
      const response = await apiClient.request<ApprovalWorkflow[]>('/api/product.serviceops/timesheets/users');
      return response.map(workflow => approvalWorkflowSchema.parse(workflow));
    },
  });
};

export const useCreateApprovalWorkflow = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (workflow: Omit<ApprovalWorkflow, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApprovalWorkflow> => {
      const response = await apiClient.request<ApprovalWorkflow>('/api/product.serviceops/timesheets/users', {
        method: 'POST',
        body: JSON.stringify(workflow),
      });
      return approvalWorkflowSchema.parse(response);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [APPROVAL_WORKFLOWS_QUERY_KEY] });
    },
  });
};