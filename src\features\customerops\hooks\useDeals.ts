import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import {
  Deal,
  DealFormValues,
  DealSearchParams,
  DealListResponse,
  DealPipelineStats,
  Quote,
  QuoteFormValues,
  QuoteLineItemFormValues,
  QuoteApprovalValues,
  dealListResponseSchema,
  dealSchema,
  quoteSchema,
  dealPipelineStatsSchema,
} from '../schemas/dealSchemas';

const DEALS_QUERY_KEY = 'deals';
const QUOTES_QUERY_KEY = 'quotes';

// Deal Query hooks
export const useDeals = (searchParams?: DealSearchParams) => {
  return useQuery({
    queryKey: [DEALS_QUERY_KEY, searchParams],
    queryFn: async (): Promise<DealListResponse> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await apiClient.request<DealListResponse>(
        `/api/product.customerops/deals?${params.toString()}`
      );
      
      return dealListResponseSchema.parse(response);
    },
  });
};

export const useDeal = (dealId: string) => {
  return useQuery({
    queryKey: [DEALS_QUERY_KEY, dealId],
    queryFn: async (): Promise<Deal> => {
      const response = await apiClient.request<Deal>(
        `/api/product.customerops/deals/${dealId}`
      );
      return dealSchema.parse(response);
    },
    enabled: !!dealId,
  });
};

export const useDealPipelineStats = (filters?: Partial<DealSearchParams>) => {
  return useQuery({
    queryKey: [DEALS_QUERY_KEY, 'pipeline-stats', filters],
    queryFn: async (): Promise<DealPipelineStats> => {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });
      }
      
      const response = await apiClient.request<DealPipelineStats>(
        `/api/product.customerops/deals/pipeline-stats?${params.toString()}`
      );
      
      return dealPipelineStatsSchema.parse(response);
    },
  });
};

// Deal Mutation hooks
export const useCreateDeal = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (deal: DealFormValues): Promise<Deal> => {
      const response = await apiClient.request<Deal>('/api/product.customerops/deals', {
        method: 'POST',
        body: JSON.stringify(deal),
      });
      return dealSchema.parse(response);
    },
    onSuccess: (newDeal) => {
      queryClient.invalidateQueries({ queryKey: [DEALS_QUERY_KEY] });
      queryClient.setQueryData([DEALS_QUERY_KEY, newDeal.id], newDeal);
    },
  });
};

export const useUpdateDeal = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      dealId, 
      deal 
    }: { 
      dealId: string; 
      deal: Partial<DealFormValues> 
    }): Promise<Deal> => {
      const response = await apiClient.request<Deal>(`/api/product.customerops/deals/${dealId}`, {
        method: 'PUT',
        body: JSON.stringify(deal),
      });
      return dealSchema.parse(response);
    },
    onSuccess: (updatedDeal) => {
      queryClient.setQueryData([DEALS_QUERY_KEY, updatedDeal.id], updatedDeal);
      queryClient.invalidateQueries({ queryKey: [DEALS_QUERY_KEY] });
    },
  });
};

export const useDeleteDeal = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (dealId: string): Promise<void> => {
      await apiClient.request(`/api/product.customerops/deals/${dealId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: (_, dealId) => {
      queryClient.removeQueries({ queryKey: [DEALS_QUERY_KEY, dealId] });
      queryClient.invalidateQueries({ queryKey: [DEALS_QUERY_KEY] });
    },
  });
};

// Quote Query hooks
export const useQuotes = (dealId?: string) => {
  return useQuery({
    queryKey: [QUOTES_QUERY_KEY, dealId],
    queryFn: async (): Promise<Quote[]> => {
      const endpoint = dealId ? `/api/product.customerops/deals/${dealId}/quotes` : '/api/product.customerops/quotes';
      const response = await apiClient.request<Quote[]>(endpoint);
      return response.map(quote => quoteSchema.parse(quote));
    },
  });
};

export const useQuote = (quoteId: string) => {
  return useQuery({
    queryKey: [QUOTES_QUERY_KEY, quoteId],
    queryFn: async (): Promise<Quote> => {
      const response = await apiClient.request<Quote>(
        `/api/product.customerops/quotes/${quoteId}`
      );
      return quoteSchema.parse(response);
    },
    enabled: !!quoteId,
  });
};

// Quote Mutation hooks
export const useCreateQuote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (quote: QuoteFormValues): Promise<Quote> => {
      const response = await apiClient.request<Quote>('/api/product.customerops/quotes', {
        method: 'POST',
        body: JSON.stringify(quote),
      });
      return quoteSchema.parse(response);
    },
    onSuccess: (newQuote) => {
      queryClient.invalidateQueries({ queryKey: [QUOTES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [DEALS_QUERY_KEY, newQuote.dealId] });
      queryClient.setQueryData([QUOTES_QUERY_KEY, newQuote.id], newQuote);
    },
  });
};

export const useUpdateQuote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      quoteId, 
      quote 
    }: { 
      quoteId: string; 
      quote: Partial<QuoteFormValues> 
    }): Promise<Quote> => {
      const response = await apiClient.request<Quote>(`/api/product.customerops/quotes/${quoteId}`, {
        method: 'PUT',
        body: JSON.stringify(quote),
      });
      return quoteSchema.parse(response);
    },
    onSuccess: (updatedQuote) => {
      queryClient.setQueryData([QUOTES_QUERY_KEY, updatedQuote.id], updatedQuote);
      queryClient.invalidateQueries({ queryKey: [QUOTES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [DEALS_QUERY_KEY, updatedQuote.dealId] });
    },
  });
};

export const useDeleteQuote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (quoteId: string): Promise<void> => {
      await apiClient.request(`/api/product.customerops/quotes/${quoteId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: (_, quoteId) => {
      queryClient.removeQueries({ queryKey: [QUOTES_QUERY_KEY, quoteId] });
      queryClient.invalidateQueries({ queryKey: [QUOTES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [DEALS_QUERY_KEY] });
    },
  });
};

// Quote Line Items
export const useAddQuoteLineItem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      quoteId, 
      lineItem 
    }: { 
      quoteId: string; 
      lineItem: QuoteLineItemFormValues 
    }): Promise<Quote> => {
      const response = await apiClient.request<Quote>(`/api/product.customerops/quotes/${quoteId}/line`, {
        method: 'POST',
        body: JSON.stringify(lineItem),
      });
      return quoteSchema.parse(response);
    },
    onSuccess: (updatedQuote) => {
      queryClient.setQueryData([QUOTES_QUERY_KEY, updatedQuote.id], updatedQuote);
      queryClient.invalidateQueries({ queryKey: [QUOTES_QUERY_KEY] });
    },
  });
};

export const useUpdateQuoteLineItem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      quoteId, 
      lineItemId, 
      lineItem 
    }: { 
      quoteId: string; 
      lineItemId: string; 
      lineItem: Partial<QuoteLineItemFormValues> 
    }): Promise<Quote> => {
      const response = await apiClient.request<Quote>(`/api/product.customerops/quotes/${quoteId}/line/${lineItemId}`, {
        method: 'PUT',
        body: JSON.stringify(lineItem),
      });
      return quoteSchema.parse(response);
    },
    onSuccess: (updatedQuote) => {
      queryClient.setQueryData([QUOTES_QUERY_KEY, updatedQuote.id], updatedQuote);
      queryClient.invalidateQueries({ queryKey: [QUOTES_QUERY_KEY] });
    },
  });
};

export const useDeleteQuoteLineItem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      quoteId, 
      lineItemId 
    }: { 
      quoteId: string; 
      lineItemId: string; 
    }): Promise<Quote> => {
      const response = await apiClient.request<Quote>(`/api/product.customerops/quotes/${quoteId}/line/${lineItemId}/delete`, {
        method: 'GET',
      });
      return quoteSchema.parse(response);
    },
    onSuccess: (updatedQuote) => {
      queryClient.setQueryData([QUOTES_QUERY_KEY, updatedQuote.id], updatedQuote);
      queryClient.invalidateQueries({ queryKey: [QUOTES_QUERY_KEY] });
    },
  });
};

// Quote Approval
export const useApproveQuote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (approvalData: QuoteApprovalValues): Promise<Quote> => {
      const response = await apiClient.request<Quote>('/api/product.customerops/quotes/approve', {
        method: 'POST',
        body: JSON.stringify(approvalData),
      });
      return quoteSchema.parse(response);
    },
    onSuccess: (updatedQuote) => {
      queryClient.setQueryData([QUOTES_QUERY_KEY, updatedQuote.id], updatedQuote);
      queryClient.invalidateQueries({ queryKey: [QUOTES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [DEALS_QUERY_KEY, updatedQuote.dealId] });
    },
  });
};

// Quote Actions
export const useSendQuote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (quoteId: string): Promise<Quote> => {
      const response = await apiClient.request<Quote>(`/api/product.customerops/quotes/${quoteId}/send`, {
        method: 'POST',
      });
      return quoteSchema.parse(response);
    },
    onSuccess: (updatedQuote) => {
      queryClient.setQueryData([QUOTES_QUERY_KEY, updatedQuote.id], updatedQuote);
      queryClient.invalidateQueries({ queryKey: [QUOTES_QUERY_KEY] });
    },
  });
};

export const useAcceptQuote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (quoteId: string): Promise<Quote> => {
      const response = await apiClient.request<Quote>(`/api/product.customerops/quotes/${quoteId}/accept`, {
        method: 'POST',
      });
      return quoteSchema.parse(response);
    },
    onSuccess: (updatedQuote) => {
      queryClient.setQueryData([QUOTES_QUERY_KEY, updatedQuote.id], updatedQuote);
      queryClient.invalidateQueries({ queryKey: [QUOTES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [DEALS_QUERY_KEY, updatedQuote.dealId] });
    },
  });
};

// Bulk Operations
export const useBulkUpdateDeals = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      dealIds, 
      updates 
    }: { 
      dealIds: string[]; 
      updates: Partial<DealFormValues> 
    }): Promise<Deal[]> => {
      const response = await apiClient.request<Deal[]>('/api/product.customerops/deals/bulk-update', {
        method: 'POST',
        body: JSON.stringify({ dealIds, updates }),
      });
      
      return response.map(deal => dealSchema.parse(deal));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [DEALS_QUERY_KEY] });
    },
  });
};

// Export functionality
export const useExportDeals = () => {
  return useMutation({
    mutationFn: async (searchParams?: DealSearchParams): Promise<Blob> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await fetch(`/api/product.customerops/deals/export?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-AUTH-TOKEN': localStorage.getItem('authToken') || '',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Export failed: ${response.status}`);
      }
      
      return response.blob();
    },
  });
};