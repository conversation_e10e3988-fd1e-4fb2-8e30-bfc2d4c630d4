import { z } from "zod";

// Ticket Status Enum
export const ticketStatusSchema = z.enum([
  'open',
  'in_progress',
  'pending',
  'resolved',
  'closed',
  'cancelled'
], {
  required_error: "Ticket status is required",
});

export type TicketStatus = z.infer<typeof ticketStatusSchema>;

// Ticket Priority Enum
export const ticketPrioritySchema = z.enum([
  'low',
  'medium',
  'high',
  'critical'
], {
  required_error: "Ticket priority is required",
});

export type TicketPriority = z.infer<typeof ticketPrioritySchema>;

// Ticket Category Enum
export const ticketCategorySchema = z.enum([
  'hardware',
  'software',
  'network',
  'account',
  'access',
  'email',
  'phone',
  'printing',
  'application',
  'security',
  'maintenance',
  'training',
  'other'
], {
  required_error: "Ticket category is required",
});

export type TicketCategory = z.infer<typeof ticketCategorySchema>;

// SLA Level Enum
export const slaLevelSchema = z.enum([
  'standard',
  'premium',
  'enterprise',
  'vip'
], {
  required_error: "SLA level is required",
});

export type SLALevel = z.infer<typeof slaLevelSchema>;

// Ticket Comment Schema
export const ticketCommentSchema = z.object({
  id: z.string(),
  ticketId: z.string(),
  entityId: z.string(),
  
  // Comment Details
  content: z.string().min(1, "Comment content is required"),
  isInternal: z.boolean().default(false),
  
  // Attachments
  attachments: z.array(z.object({
    id: z.string(),
    name: z.string(),
    url: z.string(),
    size: z.number(),
    mimeType: z.string(),
  })).default([]),
  
  // Author
  authorId: z.string(),
  authorName: z.string(),
  authorEmail: z.string().optional(),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type TicketComment = z.infer<typeof ticketCommentSchema>;

// Ticket Time Entry Schema
export const ticketTimeEntrySchema = z.object({
  id: z.string(),
  ticketId: z.string(),
  entityId: z.string(),
  
  // Time Details
  description: z.string().min(1, "Time entry description is required"),
  hours: z.number().min(0.1, "Hours must be at least 0.1"),
  date: z.string(),
  
  // Billing
  billable: z.boolean().default(true),
  hourlyRate: z.number().min(0).optional(),
  
  // User
  userId: z.string(),
  userName: z.string(),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type TicketTimeEntry = z.infer<typeof ticketTimeEntrySchema>;

// SLA Configuration Schema
export const slaConfigSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // SLA Details
  name: z.string().min(1, "SLA name is required"),
  level: slaLevelSchema,
  description: z.string().optional(),
  
  // Response Times (in minutes)
  responseTimeByPriority: z.object({
    low: z.number().min(1),
    medium: z.number().min(1),
    high: z.number().min(1),
    critical: z.number().min(1),
  }),
  
  // Resolution Times (in hours)
  resolutionTimeByPriority: z.object({
    low: z.number().min(1),
    medium: z.number().min(1),
    high: z.number().min(1),
    critical: z.number().min(1),
  }),
  
  // Business Hours
  businessHours: z.object({
    monday: z.object({ start: z.string(), end: z.string(), enabled: z.boolean() }),
    tuesday: z.object({ start: z.string(), end: z.string(), enabled: z.boolean() }),
    wednesday: z.object({ start: z.string(), end: z.string(), enabled: z.boolean() }),
    thursday: z.object({ start: z.string(), end: z.string(), enabled: z.boolean() }),
    friday: z.object({ start: z.string(), end: z.string(), enabled: z.boolean() }),
    saturday: z.object({ start: z.string(), end: z.string(), enabled: z.boolean() }),
    sunday: z.object({ start: z.string(), end: z.string(), enabled: z.boolean() }),
  }),
  
  // Holidays
  holidays: z.array(z.object({
    date: z.string(),
    name: z.string(),
  })).default([]),
  
  // Escalation Rules
  escalationEnabled: z.boolean().default(true),
  escalationThreshold: z.number().min(1).default(80), // % of SLA time
  escalationToUserId: z.string().optional(),
  escalationToUserName: z.string().optional(),
  
  // Status
  isActive: z.boolean().default(true),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type SLAConfig = z.infer<typeof slaConfigSchema>;

// Ticket Schema
export const ticketSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // Basic Information
  subject: z.string().min(1, "Subject is required"),
  description: z.string().min(1, "Description is required"),
  category: ticketCategorySchema,
  status: ticketStatusSchema,
  priority: ticketPrioritySchema,
  
  // Assignment
  assignedToUserId: z.string().optional(),
  assignedToUserName: z.string().optional(),
  assignedToGroupId: z.string().optional(),
  assignedToGroupName: z.string().optional(),
  
  // Reporter Information
  reportedByUserId: z.string(),
  reportedByUserName: z.string(),
  reportedByEmail: z.string().email(),
  reportedByPhone: z.string().optional(),
  
  // Customer Information (if external)
  customerId: z.string().optional(),
  customerName: z.string().optional(),
  
  // Asset Association
  assetId: z.string().optional(),
  assetName: z.string().optional(),
  
  // Location
  location: z.string().optional(),
  building: z.string().optional(),
  floor: z.string().optional(),
  room: z.string().optional(),
  
  // SLA Information
  slaLevel: slaLevelSchema.default('standard'),
  slaConfigId: z.string().optional(),
  responseDeadline: z.string().optional(),
  resolutionDeadline: z.string().optional(),
  slaBreached: z.boolean().default(false),
  
  // Resolution
  resolution: z.string().optional(),
  resolutionCategory: z.enum(['solved', 'workaround', 'duplicate', 'cannot_reproduce', 'not_applicable']).optional(),
  resolvedByUserId: z.string().optional(),
  resolvedByUserName: z.string().optional(),
  resolvedAt: z.string().optional(),
  
  // Closure
  closedByUserId: z.string().optional(),
  closedByUserName: z.string().optional(),
  closedAt: z.string().optional(),
  closeReason: z.string().optional(),
  
  // Satisfaction
  satisfactionRating: z.number().min(1).max(5).optional(),
  satisfactionComment: z.string().optional(),
  
  // Time Tracking
  estimatedHours: z.number().min(0).optional(),
  actualHours: z.number().min(0).default(0),
  
  // Related Tickets
  parentTicketId: z.string().optional(),
  relatedTicketIds: z.array(z.string()).default([]),
  
  // External References
  externalTicketId: z.string().optional(),
  externalSystemName: z.string().optional(),
  
  // Comments and Time Entries
  comments: z.array(ticketCommentSchema).default([]),
  timeEntries: z.array(ticketTimeEntrySchema).default([]),
  
  // Metadata
  tags: z.array(z.string()).default([]),
  customFields: z.record(z.any()).optional(),
  
  // Timestamps
  createdAt: z.string(),
  updatedAt: z.string(),
  firstResponseAt: z.string().optional(),
  lastActivityAt: z.string().optional(),
  
  // Escalation
  escalatedAt: z.string().optional(),
  escalatedToUserId: z.string().optional(),
  escalatedToUserName: z.string().optional(),
  escalationCount: z.number().default(0),
});

export type Ticket = z.infer<typeof ticketSchema>;

// Ticket Form Schemas
export const ticketFormSchema = ticketSchema.omit({
  id: true,
  comments: true,
  timeEntries: true,
  actualHours: true,
  slaBreached: true,
  resolvedAt: true,
  resolvedByUserId: true,
  resolvedByUserName: true,
  closedAt: true,
  closedByUserId: true,
  closedByUserName: true,
  firstResponseAt: true,
  lastActivityAt: true,
  escalatedAt: true,
  escalatedToUserId: true,
  escalatedToUserName: true,
  escalationCount: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  entityId: z.string().min(1, "Entity is required"),
  tags: z.array(z.string()),
});

export type TicketFormValues = z.infer<typeof ticketFormSchema>;

// Ticket Comment Form Schema
export const ticketCommentFormSchema = ticketCommentSchema.omit({
  id: true,
  ticketId: true,
  authorId: true,
  authorName: true,
  authorEmail: true,
  createdAt: true,
  updatedAt: true,
});

export type TicketCommentFormValues = z.infer<typeof ticketCommentFormSchema>;

// Ticket Time Entry Form Schema
export const ticketTimeEntryFormSchema = ticketTimeEntrySchema.omit({
  id: true,
  ticketId: true,
  userId: true,
  userName: true,
  createdAt: true,
  updatedAt: true,
});

export type TicketTimeEntryFormValues = z.infer<typeof ticketTimeEntryFormSchema>;

// Ticket Search and Filter Schemas
export const ticketSearchSchema = z.object({
  query: z.string().optional(),
  status: ticketStatusSchema.optional(),
  priority: ticketPrioritySchema.optional(),
  category: ticketCategorySchema.optional(),
  assignedToUserId: z.string().optional(),
  reportedByUserId: z.string().optional(),
  customerId: z.string().optional(),
  assetId: z.string().optional(),
  entityId: z.string().optional(),
  slaBreached: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.enum(['subject', 'priority', 'status', 'assignedToUserName', 'createdAt', 'updatedAt', 'resolutionDeadline']).default('updatedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

export type TicketSearchParams = z.infer<typeof ticketSearchSchema>;

// Ticket List Response Schema
export const ticketListResponseSchema = z.object({
  tickets: z.array(ticketSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

export type TicketListResponse = z.infer<typeof ticketListResponseSchema>;

// Ticket Statistics Schema
export const ticketStatsSchema = z.object({
  totalTickets: z.number(),
  openTickets: z.number(),
  inProgressTickets: z.number(),
  resolvedToday: z.number(),
  avgResolutionTime: z.number(), // in hours
  avgResponseTime: z.number(), // in minutes
  slaBreaches: z.number(),
  
  statusBreakdown: z.array(z.object({
    status: ticketStatusSchema,
    count: z.number(),
    percentage: z.number(),
  })),
  
  priorityBreakdown: z.array(z.object({
    priority: ticketPrioritySchema,
    count: z.number(),
    percentage: z.number(),
  })),
  
  categoryBreakdown: z.array(z.object({
    category: ticketCategorySchema,
    count: z.number(),
    percentage: z.number(),
  })),
  
  assigneeWorkload: z.array(z.object({
    userId: z.string(),
    userName: z.string(),
    openTickets: z.number(),
    avgResolutionTime: z.number(),
  })),
  
  slaPerformance: z.object({
    responseTimeCompliance: z.number(), // percentage
    resolutionTimeCompliance: z.number(), // percentage
    totalBreaches: z.number(),
    breachesByPriority: z.array(z.object({
      priority: ticketPrioritySchema,
      breaches: z.number(),
    })),
  }),
  
  trendsLast30Days: z.array(z.object({
    date: z.string(),
    opened: z.number(),
    resolved: z.number(),
    breaches: z.number(),
  })),
});

export type TicketStats = z.infer<typeof ticketStatsSchema>;

// Knowledge Base Article Schema
export const knowledgeBaseArticleSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  
  // Article Details
  title: z.string().min(1, "Title is required"),
  content: z.string().min(1, "Content is required"),
  summary: z.string().optional(),
  
  // Categorization
  category: ticketCategorySchema,
  tags: z.array(z.string()).default([]),
  
  // Visibility
  isPublic: z.boolean().default(false),
  isApproved: z.boolean().default(false),
  
  // Usage
  viewCount: z.number().default(0),
  helpfulCount: z.number().default(0),
  notHelpfulCount: z.number().default(0),
  
  // Related
  relatedArticleIds: z.array(z.string()).default([]),
  relatedTicketIds: z.array(z.string()).default([]),
  
  // Author
  authorId: z.string(),
  authorName: z.string(),
  
  // Approval
  approvedByUserId: z.string().optional(),
  approvedByUserName: z.string().optional(),
  approvedAt: z.string().optional(),
  
  // Metadata
  createdAt: z.string(),
  updatedAt: z.string(),
  lastReviewedAt: z.string().optional(),
});

export type KnowledgeBaseArticle = z.infer<typeof knowledgeBaseArticleSchema>;