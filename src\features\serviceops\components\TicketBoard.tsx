import { useState } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { 
  Plus, 
  Filter, 
  Eye, 
  Edit, 
  User, 
  Calendar, 
  Clock,
  AlertTriangle,
  MessageSquare,
  Timer,
  RefreshCw
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { useTickets, useUpdateTicket } from '../hooks/useTickets';
import { 
  Ticket, 
  TicketStatus, 
  TicketPriority, 
  TicketSearchParams 
} from '../schemas/ticketSchemas';

const statusConfig: Record<TicketStatus, { title: string; color: string }> = {
  open: { title: 'Open', color: 'bg-red-100 text-red-800 border-red-200' },
  in_progress: { title: 'In Progress', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  pending: { title: 'Pending', color: 'bg-purple-100 text-purple-800 border-purple-200' },
  resolved: { title: 'Resolved', color: 'bg-green-100 text-green-800 border-green-200' },
  closed: { title: 'Closed', color: 'bg-gray-100 text-gray-800 border-gray-200' },
  cancelled: { title: 'Cancelled', color: 'bg-orange-100 text-orange-800 border-orange-200' },
};

const priorityColors = {
  low: 'bg-green-500',
  medium: 'bg-yellow-500',
  high: 'bg-orange-500',
  critical: 'bg-red-500',
};

interface TicketBoardProps {
  onCreateTicket?: () => void;
  onEditTicket?: (ticket: Ticket) => void;
  onViewTicket?: (ticket: Ticket) => void;
}

export function TicketBoard({ onCreateTicket, onEditTicket, onViewTicket }: TicketBoardProps) {
  const [searchParams, setSearchParams] = useState<TicketSearchParams>({
    page: 1,
    limit: 100,
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  });

  const { data: ticketsData, isLoading, refetch } = useTickets(searchParams);
  const updateTicket = useUpdateTicket();

  const tickets = ticketsData?.tickets || [];

  // Group tickets by status
  const groupedTickets: Record<TicketStatus, Ticket[]> = tickets.reduce((acc, ticket) => {
    const status = ticket.status as TicketStatus;
    if (!acc[status]) {
      acc[status] = [];
    }
    acc[status].push(ticket);
    return acc;
  }, {} as Record<TicketStatus, Ticket[]>);

  // Ensure all statuses have arrays
  Object.keys(statusConfig).forEach(status => {
    if (!groupedTickets[status as TicketStatus]) {
      groupedTickets[status as TicketStatus] = [];
    }
  });

  const handleDragEnd = async (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    const ticketId = draggableId;
    const newStatus = destination.droppableId as TicketStatus;

    try {
      await updateTicket.mutateAsync({
        ticketId,
        ticket: { status: newStatus },
      });
    } catch (error) {
      console.error('Failed to update ticket status:', error);
    }
  };

  const handleSearch = (query: string) => {
    setSearchParams(prev => ({ ...prev, query, page: 1 }));
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString();
  };

  const getSLAProgress = (ticket: Ticket) => {
    if (!ticket.resolutionDeadline) return 0;
    
    const now = new Date();
    const deadline = new Date(ticket.resolutionDeadline);
    const created = new Date(ticket.createdAt);
    
    const totalTime = deadline.getTime() - created.getTime();
    const elapsedTime = now.getTime() - created.getTime();
    
    return Math.min((elapsedTime / totalTime) * 100, 100);
  };

  const TicketCard = ({ ticket, index }: { ticket: Ticket; index: number }) => (
    <Draggable draggableId={ticket.id} index={index}>
      {(provided, snapshot) => (
        <Card
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`mb-3 cursor-move transition-shadow ${
            snapshot.isDragging ? 'shadow-lg' : 'hover:shadow-md'
          }`}
        >
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-mono text-xs text-muted-foreground">#{ticket.id.slice(-6)}</span>
                  <div className={`w-2 h-2 rounded-full ${priorityColors[ticket.priority]}`} title={`${ticket.priority} priority`} />
                  {ticket.slaBreached && (
                    <AlertTriangle className="h-3 w-3 text-red-500" />
                  )}
                </div>
                <h4 className="font-medium text-sm mb-1">{ticket.subject}</h4>
                {ticket.description && (
                  <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                    {ticket.description}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2 text-xs">
              <div className="flex items-center gap-1">
                <Badge variant="outline" className="capitalize text-xs">
                  {ticket.category}
                </Badge>
              </div>

              <div className="flex items-center gap-1">
                <User className="h-3 w-3 text-muted-foreground" />
                <span>{ticket.reportedByUserName}</span>
              </div>
              
              {ticket.assignedToUserName && (
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3 text-blue-500" />
                  <span>Assigned: {ticket.assignedToUserName}</span>
                </div>
              )}
              
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3 text-muted-foreground" />
                <span>Created: {formatDate(ticket.createdAt)}</span>
              </div>

              {ticket.resolutionDeadline && (
                <div className="space-y-1">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <span>Due: {formatDateTime(ticket.resolutionDeadline)}</span>
                  </div>
                  <Progress 
                    value={getSLAProgress(ticket)} 
                    className={`h-1 ${ticket.slaBreached ? 'bg-red-100' : 'bg-green-100'}`}
                  />
                </div>
              )}
            </div>

            <div className="flex items-center justify-between mt-3">
              <div className="flex items-center gap-2">
                {ticket.comments.length > 0 && (
                  <div className="flex items-center gap-1">
                    <MessageSquare className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs">{ticket.comments.length}</span>
                  </div>
                )}
                {ticket.actualHours > 0 && (
                  <div className="flex items-center gap-1">
                    <Timer className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs">{ticket.actualHours}h</span>
                  </div>
                )}
              </div>
              
              <div className="flex gap-1">
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="h-6 text-xs px-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewTicket?.(ticket);
                  }}
                >
                  <Eye className="h-3 w-3" />
                </Button>
                <RoleGuard permissions={[PERMISSIONS['serviceops:write']]}>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="h-6 text-xs px-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditTicket?.(ticket);
                    }}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                </RoleGuard>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </Draggable>
  );

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="animate-pulse bg-muted h-8 w-64 rounded"></div>
          <div className="animate-pulse bg-muted h-10 w-32 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-96 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Ticket Board</h2>
          <p className="text-muted-foreground">
            Manage tickets through your service workflow
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <RoleGuard permissions={[PERMISSIONS['serviceops:write']]}>
            <Button onClick={onCreateTicket}>
              <Plus className="mr-2 h-4 w-4" />
              Create Ticket
            </Button>
          </RoleGuard>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search tickets..."
                value={searchParams.query || ''}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <Select 
              value={searchParams.priority || 'all'} 
              onValueChange={(value) => setSearchParams(prev => ({ 
                ...prev, 
                priority: value === 'all' ? undefined : value as TicketPriority 
              }))}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
              </SelectContent>
            </Select>
            <Select 
              value={searchParams.category || 'all'} 
              onValueChange={(value) => setSearchParams(prev => ({ 
                ...prev, 
                category: value === 'all' ? undefined : value as any 
              }))}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="hardware">Hardware</SelectItem>
                <SelectItem value="software">Software</SelectItem>
                <SelectItem value="network">Network</SelectItem>
                <SelectItem value="account">Account</SelectItem>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Ticket Board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {(Object.keys(statusConfig) as TicketStatus[]).map((status) => {
            const config = statusConfig[status];
            const statusTickets = groupedTickets[status] || [];
            
            return (
              <div key={status} className="flex flex-col">
                <div className={`p-3 rounded-t-lg border-b-2 ${config.color}`}>
                  <h3 className="font-medium text-sm">{config.title}</h3>
                  <p className="text-xs opacity-75">{statusTickets.length} tickets</p>
                </div>
                
                <Droppable droppableId={status}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`flex-1 p-2 min-h-[400px] bg-muted/20 rounded-b-lg border-l border-r border-b transition-colors ${
                        snapshot.isDraggingOver ? 'bg-muted/40' : ''
                      }`}
                    >
                      {statusTickets.map((ticket, index) => (
                        <TicketCard key={ticket.id} ticket={ticket} index={index} />
                      ))}
                      {provided.placeholder}
                      
                      {statusTickets.length === 0 && (
                        <div className="flex items-center justify-center h-32 text-muted-foreground text-sm">
                          No tickets in this status
                        </div>
                      )}
                    </div>
                  )}
                </Droppable>
              </div>
            );
          })}
        </div>
      </DragDropContext>
    </div>
  );
}