import { useState } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { 
  Plus, 
  Filter, 
  Eye, 
  Edit, 
  Trash2, 
  User, 
  Calendar, 
  DollarSign,
  Phone,
  Mail,
  Building,
  MoreHorizontal
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { useLeads, useUpdateLead, useDeleteLead, useLeadPipelineStats } from '../hooks/useLeads';
import { 
  Lead, 
  LeadStatus,
  LeadSearchParams 
} from '../schemas/leadSchemas';

const statusConfig: Record<LeadStatus, { title: string; color: string }> = {
  new: { title: 'New Leads', color: 'bg-blue-100 text-blue-800 border-blue-200' },
  contacted: { title: 'Contacted', color: 'bg-purple-100 text-purple-800 border-purple-200' },
  qualified: { title: 'Qualified', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  proposal: { title: 'Proposal', color: 'bg-orange-100 text-orange-800 border-orange-200' },
  negotiation: { title: 'Negotiation', color: 'bg-red-100 text-red-800 border-red-200' },
  won: { title: 'Won', color: 'bg-green-100 text-green-800 border-green-200' },
  lost: { title: 'Lost', color: 'bg-gray-100 text-gray-800 border-gray-200' },
  nurturing: { title: 'Nurturing', color: 'bg-indigo-100 text-indigo-800 border-indigo-200' },
};

const scoreColors = {
  hot: 'bg-red-500',
  warm: 'bg-yellow-500',
  cold: 'bg-blue-500',
};

interface LeadPipelineProps {
  onCreateLead?: () => void;
  onEditLead?: (lead: Lead) => void;
  onViewLead?: (lead: Lead) => void;
}

export function LeadPipeline({ onCreateLead, onEditLead, onViewLead }: LeadPipelineProps) {
  const [searchParams] = useState<LeadSearchParams>({
    page: 1,
    limit: 100, // Show more leads in pipeline view
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [leadToDelete, setLeadToDelete] = useState<Lead | null>(null);

  const { data: leadsData, isLoading } = useLeads(searchParams);
  const { data: pipelineStats } = useLeadPipelineStats();
  const updateLead = useUpdateLead();
  const deleteLead = useDeleteLead();

  // Group leads by status
  const groupedLeads: Record<LeadStatus, Lead[]> = leadsData?.leads.reduce((acc, lead) => {
    const status = lead.status as LeadStatus;
    if (!acc[status]) {
      acc[status] = [];
    }
    acc[status].push(lead);
    return acc;
  }, {} as Record<LeadStatus, Lead[]>) || {
    new: [],
    contacted: [],
    qualified: [],
    proposal: [],
    negotiation: [],
    won: [],
    lost: [],
    nurturing: [],
  };

  const handleDragEnd = async (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    const leadId = draggableId;
    const newStatus = destination.droppableId as LeadStatus;

    try {
      await updateLead.mutateAsync({
        leadId,
        lead: { status: newStatus },
      });
    } catch (error) {
      console.error('Failed to update lead status:', error);
    }
  };

  const handleDeleteLead = async () => {
    if (!leadToDelete) return;
    
    try {
      await deleteLead.mutateAsync(leadToDelete.id);
      setDeleteDialogOpen(false);
      setLeadToDelete(null);
    } catch (error) {
      console.error('Failed to delete lead:', error);
    }
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const LeadCard = ({ lead, index }: { lead: Lead; index: number }) => (
    <Draggable draggableId={lead.id} index={index}>
      {(provided, snapshot) => (
        <Card
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`mb-3 cursor-move transition-shadow ${
            snapshot.isDragging ? 'shadow-lg' : 'hover:shadow-md'
          }`}
        >
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm">
                    {lead.firstName} {lead.lastName}
                  </h4>
                  <div className={`w-2 h-2 rounded-full ${scoreColors[lead.score]}`} title={`${lead.score} lead`} />
                </div>
                {lead.company && (
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Building className="h-3 w-3" />
                    {lead.company}
                  </p>
                )}
                {lead.jobTitle && (
                  <p className="text-xs text-muted-foreground">{lead.jobTitle}</p>
                )}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => onViewLead?.(lead)}>
                    <Eye className="mr-2 h-3 w-3" />
                    View Details
                  </DropdownMenuItem>
                  <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
                    <DropdownMenuItem onClick={() => onEditLead?.(lead)}>
                      <Edit className="mr-2 h-3 w-3" />
                      Edit Lead
                    </DropdownMenuItem>
                  </RoleGuard>
                  <DropdownMenuItem>
                    <Phone className="mr-2 h-3 w-3" />
                    Call Lead
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Mail className="mr-2 h-3 w-3" />
                    Send Email
                  </DropdownMenuItem>
                  <RoleGuard permissions={[PERMISSIONS['customerops:delete']]}>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-red-600"
                      onClick={() => {
                        setLeadToDelete(lead);
                        setDeleteDialogOpen(true);
                      }}
                    >
                      <Trash2 className="mr-2 h-3 w-3" />
                      Delete Lead
                    </DropdownMenuItem>
                  </RoleGuard>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="space-y-2 text-xs">
              {lead.email && (
                <div className="flex items-center gap-1">
                  <Mail className="h-3 w-3 text-muted-foreground" />
                  <span className="truncate">{lead.email}</span>
                </div>
              )}
              {lead.phone && (
                <div className="flex items-center gap-1">
                  <Phone className="h-3 w-3 text-muted-foreground" />
                  <span>{lead.phone}</span>
                </div>
              )}
              {lead.estimatedValue && (
                <div className="flex items-center gap-1">
                  <DollarSign className="h-3 w-3 text-muted-foreground" />
                  <span>{formatCurrency(lead.estimatedValue)}</span>
                </div>
              )}
              {lead.estimatedCloseDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3 text-muted-foreground" />
                  <span>{formatDate(lead.estimatedCloseDate)}</span>
                </div>
              )}
              {lead.assignedUserName && (
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3 text-muted-foreground" />
                  <span>{lead.assignedUserName}</span>
                </div>
              )}
            </div>

            {lead.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {lead.tags.slice(0, 2).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                    {tag}
                  </Badge>
                ))}
                {lead.tags.length > 2 && (
                  <Badge variant="secondary" className="text-xs px-1 py-0">
                    +{lead.tags.length - 2}
                  </Badge>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </Draggable>
  );

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="animate-pulse bg-muted h-8 w-64 rounded"></div>
          <div className="animate-pulse bg-muted h-10 w-32 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-8 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-96 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Lead Pipeline</h2>
          <p className="text-muted-foreground">
            Manage and track leads through your sales funnel
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
            <Button onClick={onCreateLead}>
              <Plus className="mr-2 h-4 w-4" />
              Add Lead
            </Button>
          </RoleGuard>
        </div>
      </div>

      {/* Pipeline Stats */}
      {pipelineStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pipelineStats.totalLeads}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pipelineStats.conversionRate.toFixed(1)}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pipeline Value</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(pipelineStats.totalEstimatedValue)}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Avg. Time to Convert</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pipelineStats.averageTimeToConversion} days</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Pipeline Board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4">
          {(Object.keys(statusConfig) as LeadStatus[]).map((status) => {
            const config = statusConfig[status];
            const statusLeads = groupedLeads[status] || [];
            
            return (
              <div key={status} className="flex flex-col">
                <div className={`p-3 rounded-t-lg border-b-2 ${config.color}`}>
                  <h3 className="font-medium text-sm">{config.title}</h3>
                  <p className="text-xs opacity-75">{statusLeads.length} leads</p>
                </div>
                
                <Droppable droppableId={status}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`flex-1 p-2 min-h-[400px] bg-muted/20 rounded-b-lg border-l border-r border-b transition-colors ${
                        snapshot.isDraggingOver ? 'bg-muted/40' : ''
                      }`}
                    >
                      {statusLeads.map((lead, index) => (
                        <LeadCard key={lead.id} lead={lead} index={index} />
                      ))}
                      {provided.placeholder}
                      
                      {statusLeads.length === 0 && (
                        <div className="flex items-center justify-center h-32 text-muted-foreground text-sm">
                          No leads in this stage
                        </div>
                      )}
                    </div>
                  )}
                </Droppable>
              </div>
            );
          })}
        </div>
      </DragDropContext>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Lead</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{leadToDelete?.firstName} {leadToDelete?.lastName}"? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteLead}
              disabled={deleteLead.isPending}
            >
              {deleteLead.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}