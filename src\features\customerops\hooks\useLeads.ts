import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import {
  Lead,
  LeadFormValues,
  LeadSearchParams,
  LeadListResponse,
  LeadActivityFormValues,
  LeadActivity,
  LeadConversionValues,
  LeadPipelineStats,
  leadListResponseSchema,
  leadSchema,
  leadActivitySchema,
  leadPipelineStatsSchema,
} from '../schemas/leadSchemas';

const LEADS_QUERY_KEY = 'leads';

// Query hooks
export const useLeads = (searchParams?: LeadSearchParams) => {
  return useQuery({
    queryKey: [LEADS_QUERY_KEY, searchParams],
    queryFn: async (): Promise<LeadListResponse> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await apiClient.request<LeadListResponse>(
        `/api/product.customerops/leads?${params.toString()}`
      );
      
      return leadListResponseSchema.parse(response);
    },
  });
};

export const useLead = (leadId: string) => {
  return useQuery({
    queryKey: [LEADS_QUERY_KEY, leadId],
    queryFn: async (): Promise<Lead> => {
      const response = await apiClient.request<Lead>(
        `/api/product.customerops/leads/${leadId}`
      );
      return leadSchema.parse(response);
    },
    enabled: !!leadId,
  });
};

export const useLeadPipelineStats = (filters?: Partial<LeadSearchParams>) => {
  return useQuery({
    queryKey: [LEADS_QUERY_KEY, 'pipeline-stats', filters],
    queryFn: async (): Promise<LeadPipelineStats> => {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });
      }
      
      const response = await apiClient.request<LeadPipelineStats>(
        `/api/product.customerops/leads/pipeline-stats?${params.toString()}`
      );
      
      return leadPipelineStatsSchema.parse(response);
    },
  });
};

// Mutation hooks
export const useCreateLead = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (lead: LeadFormValues): Promise<Lead> => {
      const response = await apiClient.request<Lead>('/api/product.customerops/leads', {
        method: 'POST',
        body: JSON.stringify(lead),
      });
      return leadSchema.parse(response);
    },
    onSuccess: (newLead) => {
      // Invalidate and refetch leads list
      queryClient.invalidateQueries({ queryKey: [LEADS_QUERY_KEY] });
      
      // Add the new lead to the cache
      queryClient.setQueryData([LEADS_QUERY_KEY, newLead.id], newLead);
    },
  });
};

export const useUpdateLead = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      leadId, 
      lead 
    }: { 
      leadId: string; 
      lead: Partial<LeadFormValues> 
    }): Promise<Lead> => {
      const response = await apiClient.request<Lead>(`/api/product.customerops/leads/${leadId}`, {
        method: 'PUT',
        body: JSON.stringify(lead),
      });
      return leadSchema.parse(response);
    },
    onSuccess: (updatedLead) => {
      // Update the lead in the cache
      queryClient.setQueryData([LEADS_QUERY_KEY, updatedLead.id], updatedLead);
      
      // Invalidate the leads list to ensure it's fresh
      queryClient.invalidateQueries({ queryKey: [LEADS_QUERY_KEY] });
    },
  });
};

export const useDeleteLead = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (leadId: string): Promise<void> => {
      await apiClient.request(`/api/product.customerops/leads/${leadId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: (_, leadId) => {
      // Remove the lead from the cache
      queryClient.removeQueries({ queryKey: [LEADS_QUERY_KEY, leadId] });
      
      // Invalidate the leads list
      queryClient.invalidateQueries({ queryKey: [LEADS_QUERY_KEY] });
    },
  });
};

// Lead Activities
export const useAddLeadActivity = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      leadId, 
      activity 
    }: { 
      leadId: string; 
      activity: LeadActivityFormValues 
    }): Promise<LeadActivity> => {
      const response = await apiClient.request<LeadActivity>(`/api/product.customerops/leads/${leadId}/activities`, {
        method: 'POST',
        body: JSON.stringify(activity),
      });
      return leadActivitySchema.parse(response);
    },
    onSuccess: (_, { leadId }) => {
      // Invalidate the lead to refetch with new activity
      queryClient.invalidateQueries({ queryKey: [LEADS_QUERY_KEY, leadId] });
      queryClient.invalidateQueries({ queryKey: [LEADS_QUERY_KEY] });
    },
  });
};

// Lead Conversion
export const useConvertLead = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (conversionData: LeadConversionValues): Promise<{ lead: Lead; customerId: string }> => {
      const response = await apiClient.request<{ lead: Lead; customerId: string }>('/api/product.customerops/leads/convert', {
        method: 'POST',
        body: JSON.stringify(conversionData),
      });
      
      return {
        lead: leadSchema.parse(response.lead),
        customerId: response.customerId,
      };
    },
    onSuccess: ({ lead }) => {
      // Update the converted lead in cache
      queryClient.setQueryData([LEADS_QUERY_KEY, lead.id], lead);
      
      // Invalidate leads and customers lists
      queryClient.invalidateQueries({ queryKey: [LEADS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
  });
};

// Lead Assignment
export const useAssignLead = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      leadId, 
      userId, 
      userName 
    }: { 
      leadId: string; 
      userId: string; 
      userName: string; 
    }): Promise<Lead> => {
      const response = await apiClient.request<Lead>(`/api/product.customerops/leads/${leadId}/assign`, {
        method: 'POST',
        body: JSON.stringify({ userId, userName }),
      });
      return leadSchema.parse(response);
    },
    onSuccess: (updatedLead) => {
      // Update the lead in the cache
      queryClient.setQueryData([LEADS_QUERY_KEY, updatedLead.id], updatedLead);
      
      // Invalidate the leads list
      queryClient.invalidateQueries({ queryKey: [LEADS_QUERY_KEY] });
    },
  });
};

// Bulk Operations
export const useBulkUpdateLeads = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      leadIds, 
      updates 
    }: { 
      leadIds: string[]; 
      updates: Partial<LeadFormValues> 
    }): Promise<Lead[]> => {
      const response = await apiClient.request<Lead[]>('/api/product.customerops/leads/bulk-update', {
        method: 'POST',
        body: JSON.stringify({ leadIds, updates }),
      });
      
      return response.map(lead => leadSchema.parse(lead));
    },
    onSuccess: () => {
      // Invalidate all lead queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: [LEADS_QUERY_KEY] });
    },
  });
};

// Search and export functionality
export const useSearchLeads = (query: string) => {
  return useQuery({
    queryKey: [LEADS_QUERY_KEY, 'search', query],
    queryFn: async (): Promise<Lead[]> => {
      if (!query.trim()) return [];
      
      const response = await apiClient.request<Lead[]>(
        `/api/product.customerops/leads?search=${encodeURIComponent(query)}`
      );
      
      return response.map(lead => leadSchema.parse(lead));
    },
    enabled: query.length > 2, // Only search when query is at least 3 characters
  });
};

export const useExportLeads = () => {
  return useMutation({
    mutationFn: async (searchParams?: LeadSearchParams): Promise<Blob> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await fetch(`/api/product.customerops/leads/export?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-AUTH-TOKEN': localStorage.getItem('authToken') || '',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Export failed: ${response.status}`);
      }
      
      return response.blob();
    },
  });
};