import { z } from "zod";

// Customer Type Enum
export const customerTypeSchema = z.enum(['individual', 'business'], {
  required_error: "Customer type is required",
});

export type CustomerType = z.infer<typeof customerTypeSchema>;

// Customer Status Enum
export const customerStatusSchema = z.enum(['active', 'inactive', 'prospect', 'churned'], {
  required_error: "Customer status is required",
});

export type CustomerStatus = z.infer<typeof customerStatusSchema>;

// Contact Schema
export const contactSchema = z.object({
  id: z.string(),
  customerId: z.string(),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address").optional(),
  phone: z.string().optional(),
  position: z.string().optional(),
  isPrimary: z.boolean().default(false),
  isActive: z.boolean().default(true),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type Contact = z.infer<typeof contactSchema>;

// Customer Schema
export const customerSchema = z.object({
  id: z.string(),
  entityId: z.string(),
  name: z.string().min(2, "Customer name must be at least 2 characters"),
  type: customerTypeSchema,
  status: customerStatusSchema,
  email: z.string().email("Invalid email address").optional(),
  phone: z.string().optional(),
  website: z.string().url("Invalid website URL").optional(),
  
  // Address fields
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().optional(),
  }).optional(),
  
  // Business specific fields
  industry: z.string().optional(),
  companySize: z.string().optional(),
  taxId: z.string().optional(),
  
  // Relationship fields
  parentCustomerId: z.string().optional(),
  accountManagerId: z.string().optional(),
  
  // Metadata
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  
  // Contacts
  contacts: z.array(contactSchema).default([]),
  
  // Timestamps
  createdAt: z.string(),
  updatedAt: z.string(),
  
  // Analytics
  totalRevenue: z.number().default(0),
  lastContactDate: z.string().optional(),
  acquisitionSource: z.string().optional(),
});

export type Customer = z.infer<typeof customerSchema>;

// Customer Form Schemas
export const customerFormSchema = customerSchema.omit({
  id: true,
  contacts: true,
  createdAt: true,
  updatedAt: true,
  totalRevenue: true,
}).extend({
  entityId: z.string().min(1, "Entity is required"),
  tags: z.array(z.string()),
});

export type CustomerFormValues = z.infer<typeof customerFormSchema>;

export const contactFormSchema = contactSchema.omit({
  id: true,
  customerId: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  isPrimary: z.boolean(),
  isActive: z.boolean(),
});

export type ContactFormValues = z.infer<typeof contactFormSchema>;

// Search and Filter Schemas
export const customerSearchSchema = z.object({
  query: z.string().optional(),
  type: customerTypeSchema.optional(),
  status: customerStatusSchema.optional(),
  entityId: z.string().optional(),
  accountManagerId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt', 'totalRevenue']).default('updatedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

export type CustomerSearchParams = z.infer<typeof customerSearchSchema>;

// Customer List Response Schema
export const customerListResponseSchema = z.object({
  customers: z.array(customerSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

export type CustomerListResponse = z.infer<typeof customerListResponseSchema>;