@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:where(.dark, .dark*));

:root {
  --radius: 0.625rem;
  --brand-blue: oklch(0.65 0.15 250); /* Approximate blue from the outer ring */
  --brand-green: oklch(
    0.7 0.15 140
  ); /* Approximate green from the middle ring */
  --brand-orange: oklch(
    0.8 0.2 70
  ); /* Approximate orange from the inner ring */
  --background: oklch(0.95 0.01 30); /* Light gray background */

  /* Derived colours (for UI elements) */
  --foreground: oklch(0.1 0.01 30); /* Very dark gray for text, almost black */
  --primary: var(--brand-blue); /* Primary action color */
  --primary-foreground: oklch(
    0.98 0.01 30
  ); /* Near white, for text on primary */
  --secondary: var(--brand-green); /* Secondary accent color */
  --secondary-foreground: oklch(0.1 0.01 30); /* Dark text on secondary */
  --accent: var(--brand-orange); /*  Accent color for highlights */
  --accent-foreground: oklch(0.1 0.01 30); /* Dark text on accent */
  --muted: oklch(0.9 0.01 30); /* Slightly darker gray for muted elements */
  --muted-foreground: oklch(0.5 0.01 30); /* Medium gray for muted text */
  --card: oklch(1 0 0); /* Near White */
  --card-foreground: oklch(0.145 0 0); /* Almost Black */
  --popover: oklch(1 0 0); /* Near White */
  --popover-foreground: oklch(0.145 0 0); /* Almost Black */
  --destructive: oklch(0.5 0.2 20); /* A shade of red */
  --border: oklch(0.85 0.01 30); /* Light gray for borders */
  --input: oklch(0.98 0.01 30); /* Very light gray for input backgrounds */
  --ring: var(--brand-blue); /* Blue for focus rings */

  --chart-1: var(--brand-blue);
  --chart-2: var(--brand-green);
  --chart-3: var(--brand-orange);
  --chart-4: oklch(0.7 0.1 280); /* A slightly different blue/purple */
  --chart-5: oklch(0.6 0.1 50); /* A slightly different orange/yellow */

  /* Sidebar specific colours */
  --sidebar: oklch(0.98 0.01 30);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--brand-blue);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--brand-orange);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);

  /* Link colours */
  --link: var(--brand-blue);
  --link-hover: oklch(0.35 0.15 250); /* A darker shade of blue on hover */
}

.dark {
  /* Core colours (from the image) */
  --brand-blue: oklch(0.5 0.2 250); /* Approximate blue from the outer ring */
  --brand-green: oklch(
    0.6 0.2 140
  ); /* Approximate green from the middle ring */
  --brand-orange: oklch(
    0.7 0.2 70
  ); /* Approximate orange from the inner ring */
  --background: oklch(0.1 0.01 30); /* Dark gray background */

  /* Derived colours (for UI elements) */
  --foreground: oklch(0.98 0.01 30); /* Near White, for text  */
  --primary: var(--brand-blue); /* Primary action color */
  --primary-foreground: oklch(0.1 0.01 30); /* Dark gray for text on primary */
  --secondary: var(--brand-green); /* Secondary accent color */
  --secondary-foreground: oklch(
    0.98 0.01 30
  ); /* near white text on secondary */
  --accent: var(--brand-orange); /*  Accent color for highlights */
  --accent-foreground: oklch(0.98 0.01 30); /* White text on accent */
  --muted: oklch(0.2 0.01 30); /* Slightly darker gray for muted elements */
  --muted-foreground: oklch(0.6 0.01 30); /* Medium gray for muted text */
  --card: oklch(0.145 0 0); /* Near Black */
  --card-foreground: oklch(1 0 0); /* Near White */
  --popover: oklch(0.145 0 0); /* Near Black */
  --popover-foreground: oklch(1 0 0); /* Near White */
  --destructive: oklch(0.7 0.2 20); /* A shade of red */
  --border: oklch(0.25 0.01 30); /* Light gray for borders */
  --input: oklch(0.2 0.01 30); /* Very light gray for input backgrounds */
  --ring: var(--brand-blue); /* Blue for focus rings */

  --chart-1: var(--brand-blue);
  --chart-2: var(--brand-green);
  --chart-3: var(--brand-orange);
  --chart-4: oklch(0.5 0.1 280); /* A slightly different blue/purple */
  --chart-5: oklch(0.4 0.1 50); /* A slightly different orange/yellow */

  /* Sidebar specific colours */
  --sidebar: oklch(0.145 0 0);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--brand-blue);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--brand-orange);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);

  /* Link colours */
  --link: var(--brand-blue);
  --link-hover: oklch(0.75 0.15 250); /* A lighter shade of blue on hover */

  li[data-slot='navigation-menu-item'] > a {
    color: #ffffff;
  }

  li[data-slot='navigation-menu-item'] > a:hover {
    color: var(--brand-orange);
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-link: var(--link);
  --color-link-hover: var(--link-hover);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  h1 {
    @apply text-5xl font-semibold mb-5;
  }

  h2 {
    @apply text-4xl font-semibold;
  }

  h3 {
    @apply text-3xl font-semibold;
  }

  h4 {
    @apply text-2xl font-semibold;
  }

  p {
    @apply leading-7 [&:not(:first-of-type)]:mt-6;
  }

  :not(header) > a {
    @apply text-link underline underline-offset-4 transition-colors hover:text-link-hover;
  }

  :not(header) > button {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background
    bg-primary text-primary-foreground hover:bg-primary/80
    dark:bg-secondary dark:text-secondary-foreground dark:hover:bg-secondary/80;
  }

  input,
  textarea {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  select {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  textarea {
    @apply min-h-[100px] resize-none;
  }

  .container {
    @apply container mx-auto px-4;
  }

  /* Additional styles for common elements */
  hr {
    @apply border-border my-8;
  }

  li[data-slot='navigation-menu-item'] > a {
    text-decoration: none;
  }

  li[data-slot='navigation-menu-item']:hover a {
    background-color: var(--brand-orange);
  }
}
