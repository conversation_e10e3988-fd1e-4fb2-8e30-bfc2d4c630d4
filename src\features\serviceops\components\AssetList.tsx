import { useState } from 'react';
import { 
  Eye, 
  Edit, 
  Trash2, 
  Plus, 
  Search, 
  Filter, 
  Download, 
  RefreshCw, 
  MoreHorizontal,
  Calendar,
  DollarSign,
  User,
  MapPin,
  Wrench,
  UserCheck,
  Package
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { useAssets, useDeleteAsset, useBulkUpdateAssets, useExportAssets } from '../hooks/useAssets';
import { 
  Asset, 
  AssetStatus,
  AssetCondition,
  AssetSearchParams 
} from '../schemas/assetSchemas';

interface AssetListProps {
  onCreateAsset?: () => void;
  onEditAsset?: (asset: Asset) => void;
  onViewAsset?: (asset: Asset) => void;
  onAssignAsset?: (asset: Asset) => void;
  onScheduleMaintenance?: (asset: Asset) => void;
}

export function AssetList({ 
  onCreateAsset, 
  onEditAsset, 
  onViewAsset, 
  onAssignAsset,
  onScheduleMaintenance 
}: AssetListProps) {
  const [searchParams, setSearchParams] = useState<AssetSearchParams>({
    page: 1,
    limit: 20,
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  });
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState<Asset | null>(null);
  const [filtersOpen, setFiltersOpen] = useState(false);

  const { data: assetsData, isLoading, refetch } = useAssets(searchParams);
  const deleteAsset = useDeleteAsset();
  const bulkUpdateAssets = useBulkUpdateAssets();
  const exportAssets = useExportAssets();

  const assets = assetsData?.assets || [];
  const totalPages = assetsData?.totalPages || 1;

  const handleSearch = (query: string) => {
    setSearchParams(prev => ({ ...prev, query, page: 1 }));
  };

  const handleFilterChange = (key: keyof AssetSearchParams, value: unknown) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleSort = (sortBy: string) => {
    setSearchParams(prev => ({
      ...prev,
      sortBy: sortBy as AssetSearchParams['sortBy'],
      sortOrder: prev.sortBy === sortBy && prev.sortOrder === 'asc' ? 'desc' : 'asc',
      page: 1,
    }));
  };

  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({ ...prev, page }));
  };

  const handleSelectAsset = (assetId: string, selected: boolean) => {
    if (selected) {
      setSelectedAssets(prev => [...prev, assetId]);
    } else {
      setSelectedAssets(prev => prev.filter(id => id !== assetId));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedAssets(assets.map(asset => asset.id));
    } else {
      setSelectedAssets([]);
    }
  };

  const handleBulkStatusUpdate = async (status: AssetStatus) => {
    if (selectedAssets.length === 0) return;
    
    try {
      await bulkUpdateAssets.mutateAsync({
        assetIds: selectedAssets,
        updates: { status },
      });
      setSelectedAssets([]);
    } catch (error) {
      console.error('Failed to update assets:', error);
    }
  };

  const handleDeleteAsset = async () => {
    if (!assetToDelete) return;
    
    try {
      await deleteAsset.mutateAsync(assetToDelete.id);
      setDeleteDialogOpen(false);
      setAssetToDelete(null);
    } catch (error) {
      console.error('Failed to delete asset:', error);
    }
  };

  const handleExport = async () => {
    try {
      const blob = await exportAssets.mutateAsync(searchParams);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `assets-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to export assets:', error);
    }
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: AssetStatus) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      maintenance: 'bg-yellow-100 text-yellow-800',
      retired: 'bg-orange-100 text-orange-800',
      disposed: 'bg-red-100 text-red-800',
      pending_disposal: 'bg-red-100 text-red-800',
      lost: 'bg-purple-100 text-purple-800',
      stolen: 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getConditionColor = (condition: AssetCondition) => {
    const colors = {
      excellent: 'bg-green-100 text-green-800',
      good: 'bg-blue-100 text-blue-800',
      fair: 'bg-yellow-100 text-yellow-800',
      poor: 'bg-orange-100 text-orange-800',
      broken: 'bg-red-100 text-red-800',
    };
    return colors[condition] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="animate-pulse bg-muted h-8 w-64 rounded"></div>
          <div className="animate-pulse bg-muted h-10 w-32 rounded"></div>
        </div>
        <div className="animate-pulse bg-muted h-96 rounded"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Asset Management</h2>
          <p className="text-muted-foreground">
            Track and manage your organization's fixed assets
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <RoleGuard permissions={[PERMISSIONS['serviceops:write']]}>
            <Button onClick={onCreateAsset}>
              <Plus className="mr-2 h-4 w-4" />
              Add Asset
            </Button>
          </RoleGuard>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search & Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search assets by name, serial number, or asset tag..."
                  value={searchParams.query || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full"
                />
              </div>
              <Sheet open={filtersOpen} onOpenChange={setFiltersOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                  </Button>
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader>
                    <SheetTitle>Filter Assets</SheetTitle>
                    <SheetDescription>
                      Refine your asset search with these filters
                    </SheetDescription>
                  </SheetHeader>
                  <div className="space-y-4 mt-4">
                    <div>
                      <Label>Category</Label>
                      <Select 
                        value={searchParams.category || 'all'} 
                        onValueChange={(value) => handleFilterChange('category', value === 'all' ? undefined : value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="All categories" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          <SelectItem value="computer">Computer</SelectItem>
                          <SelectItem value="laptop">Laptop</SelectItem>
                          <SelectItem value="mobile_device">Mobile Device</SelectItem>
                          <SelectItem value="tablet">Tablet</SelectItem>
                          <SelectItem value="printer">Printer</SelectItem>
                          <SelectItem value="monitor">Monitor</SelectItem>
                          <SelectItem value="networking">Networking</SelectItem>
                          <SelectItem value="server">Server</SelectItem>
                          <SelectItem value="software_license">Software License</SelectItem>
                          <SelectItem value="vehicle">Vehicle</SelectItem>
                          <SelectItem value="furniture">Furniture</SelectItem>
                          <SelectItem value="equipment">Equipment</SelectItem>
                          <SelectItem value="tool">Tool</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label>Status</Label>
                      <Select 
                        value={searchParams.status || 'all'} 
                        onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="All statuses" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                          <SelectItem value="maintenance">Maintenance</SelectItem>
                          <SelectItem value="retired">Retired</SelectItem>
                          <SelectItem value="disposed">Disposed</SelectItem>
                          <SelectItem value="pending_disposal">Pending Disposal</SelectItem>
                          <SelectItem value="lost">Lost</SelectItem>
                          <SelectItem value="stolen">Stolen</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Condition</Label>
                      <Select 
                        value={searchParams.condition || 'all'} 
                        onValueChange={(value) => handleFilterChange('condition', value === 'all' ? undefined : value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="All conditions" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Conditions</SelectItem>
                          <SelectItem value="excellent">Excellent</SelectItem>
                          <SelectItem value="good">Good</SelectItem>
                          <SelectItem value="fair">Fair</SelectItem>
                          <SelectItem value="poor">Poor</SelectItem>
                          <SelectItem value="broken">Broken</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Location</Label>
                      <Input
                        placeholder="Filter by location"
                        value={searchParams.location || ''}
                        onChange={(e) => handleFilterChange('location', e.target.value || undefined)}
                      />
                    </div>

                    <div>
                      <Label>Manufacturer</Label>
                      <Input
                        placeholder="Filter by manufacturer"
                        value={searchParams.manufacturer || ''}
                        onChange={(e) => handleFilterChange('manufacturer', e.target.value || undefined)}
                      />
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
            
            {/* Bulk Actions */}
            {selectedAssets.length > 0 && (
              <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                <span className="text-sm text-muted-foreground">
                  {selectedAssets.length} asset{selectedAssets.length > 1 ? 's' : ''} selected
                </span>
                <div className="flex gap-2">
                  <Select onValueChange={handleBulkStatusUpdate}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Change status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="retired">Retired</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedAssets([])}
                  >
                    Clear Selection
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Assets Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedAssets.length === assets.length && assets.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted"
                  onClick={() => handleSort('name')}
                >
                  Asset
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted"
                  onClick={() => handleSort('category')}
                >
                  Category
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Condition</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Assigned To</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted"
                  onClick={() => handleSort('currentValue')}
                >
                  Value
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted"
                  onClick={() => handleSort('purchaseDate')}
                >
                  Purchase Date
                </TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {assets.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8">
                    <div className="text-muted-foreground">
                      No assets found matching your criteria.
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                assets.map((asset) => (
                  <TableRow 
                    key={asset.id} 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => onViewAsset?.(asset)}
                  >
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      <Checkbox
                        checked={selectedAssets.includes(asset.id)}
                        onCheckedChange={(checked) => handleSelectAsset(asset.id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{asset.name}</div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          {asset.assetTag && (
                            <>
                              <Package className="h-3 w-3" />
                              <span>{asset.assetTag}</span>
                            </>
                          )}
                          {asset.serialNumber && (
                            <span className="text-xs">S/N: {asset.serialNumber}</span>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="capitalize">
                        {asset.category.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getStatusColor(asset.status)}>
                        {asset.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getConditionColor(asset.condition)}>
                        {asset.condition}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {asset.location && (
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{asset.location}</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {asset.assignedToUserName && (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span>{asset.assignedToUserName}</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span>{formatCurrency(asset.currentValue || asset.purchasePrice)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>{formatDate(asset.purchaseDate)}</span>
                      </div>
                    </TableCell>
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => onViewAsset?.(asset)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <RoleGuard permissions={[PERMISSIONS['serviceops:write']]}>
                            <DropdownMenuItem onClick={() => onEditAsset?.(asset)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Asset
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onAssignAsset?.(asset)}>
                              <UserCheck className="mr-2 h-4 w-4" />
                              Assign Asset
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onScheduleMaintenance?.(asset)}>
                              <Wrench className="mr-2 h-4 w-4" />
                              Schedule Maintenance
                            </DropdownMenuItem>
                          </RoleGuard>
                          <RoleGuard permissions={[PERMISSIONS['serviceops:delete']]}>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => {
                                setAssetToDelete(asset);
                                setDeleteDialogOpen(true);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Asset
                            </DropdownMenuItem>
                          </RoleGuard>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => handlePageChange(searchParams.page - 1)}
            disabled={searchParams.page <= 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {searchParams.page} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => handlePageChange(searchParams.page + 1)}
            disabled={searchParams.page >= totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Asset</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{assetToDelete?.name}"? 
              This action cannot be undone and will also delete all associated maintenance records and assignments.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteAsset}
              disabled={deleteAsset.isPending}
            >
              {deleteAsset.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}