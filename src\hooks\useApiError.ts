import { useCallback } from 'react';
import { useNavigate } from 'react-router';
import { useAuth } from '../features/auth/hooks/useAuth';

export const useApiError = () => {
  const navigate = useNavigate();
  const { logout } = useAuth();

  return useCallback((error: Error) => {
    if (error.message.includes('401')) {
      logout(() => navigate('/login'));
    }
    // Handle other error types as needed
  }, [navigate, logout]);
};