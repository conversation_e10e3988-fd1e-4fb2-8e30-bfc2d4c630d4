import * as React from 'react';
import * as SwitchPrimitive from '@radix-ui/react-switch';
import { Sun, Moon } from 'lucide-react';

import { cn } from '@/lib/utils';

function SwitchTheme({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  const [isDark, setIsDark] = React.useState(
    document.documentElement.classList.contains('dark')
  );

  const toggleDarkMode = () => {
    const html = document.documentElement;
    html.classList.toggle('dark');
    html.classList.toggle('scheme-dark');
    html.classList.toggle('scheme-light');
    setIsDark(!isDark);
  };

  return (
    <SwitchPrimitive.Root
      data-slot='switch'
      className={cn(
        'peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-[1.15rem] w-12 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50',
        className
      )}
      checked={isDark}
      onCheckedChange={toggleDarkMode}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot='switch-thumb'
        className={cn(
          'pointer-events-none flex size-5 items-center justify-center rounded-full bg-background ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%+4px)] data-[state=unchecked]:translate-x-0',
          isDark ? 'dark:bg-primary-foreground' : 'dark:bg-foreground'
        )}
      >
        {isDark ? (
          <Moon className='h-3 w-3 text-foreground' />
        ) : (
          <Sun className='h-3 w-3 text-foreground' />
        )}
      </SwitchPrimitive.Thumb>
    </SwitchPrimitive.Root>
  );
}

export { SwitchTheme };
