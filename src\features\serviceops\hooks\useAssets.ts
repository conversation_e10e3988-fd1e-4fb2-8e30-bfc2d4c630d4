import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import {
  Asset,
  AssetFormValues,
  AssetSearchParams,
  AssetListResponse,
  AssetStats,
  AssetMaintenance,
  AssetMaintenanceFormValues,
  AssetAssignment,
  AssetAssignmentFormValues,
  AssetAudit,
  assetListResponseSchema,
  assetSchema,
  assetStatsSchema,
  assetMaintenanceSchema,
  assetAssignmentSchema,
  assetAuditSchema,
} from '../schemas/assetSchemas';

const ASSETS_QUERY_KEY = 'assets';
const ASSET_MAINTENANCE_QUERY_KEY = 'asset-maintenance';
const ASSET_ASSIGNMENTS_QUERY_KEY = 'asset-assignments';
const ASSET_AUDITS_QUERY_KEY = 'asset-audits';

// Asset Query hooks
export const useAssets = (searchParams?: AssetSearchParams) => {
  return useQuery({
    queryKey: [ASSETS_QUERY_KEY, searchParams],
    queryFn: async (): Promise<AssetListResponse> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await apiClient.request<AssetListResponse>(
        `/api/product.serviceops/assetmanager/assets?${params.toString()}`
      );
      
      return assetListResponseSchema.parse(response);
    },
  });
};

export const useAsset = (assetId: string) => {
  return useQuery({
    queryKey: [ASSETS_QUERY_KEY, assetId],
    queryFn: async (): Promise<Asset> => {
      const response = await apiClient.request<Asset>(
        `/api/product.serviceops/assetmanager/assets/${assetId}`
      );
      return assetSchema.parse(response);
    },
    enabled: !!assetId,
  });
};

export const useAssetStats = (filters?: Partial<AssetSearchParams>) => {
  return useQuery({
    queryKey: [ASSETS_QUERY_KEY, 'stats', filters],
    queryFn: async (): Promise<AssetStats> => {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });
      }
      
      const response = await apiClient.request<AssetStats>(
        `/api/product.serviceops/assetmanager/assets/stats?${params.toString()}`
      );
      
      return assetStatsSchema.parse(response);
    },
  });
};

// Asset Mutation hooks
export const useCreateAsset = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (asset: AssetFormValues): Promise<Asset> => {
      const response = await apiClient.request<Asset>('/api/product.serviceops/assetmanager/fixedasset/new', {
        method: 'POST',
        body: JSON.stringify(asset),
      });
      return assetSchema.parse(response);
    },
    onSuccess: (newAsset) => {
      queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY] });
      queryClient.setQueryData([ASSETS_QUERY_KEY, newAsset.id], newAsset);
    },
  });
};

export const useUpdateAsset = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      assetId, 
      asset 
    }: { 
      assetId: string; 
      asset: Partial<AssetFormValues> 
    }): Promise<Asset> => {
      const response = await apiClient.request<Asset>(`/api/product.serviceops/assetmanager/assets/${assetId}`, {
        method: 'PUT',
        body: JSON.stringify(asset),
      });
      return assetSchema.parse(response);
    },
    onSuccess: (updatedAsset) => {
      queryClient.setQueryData([ASSETS_QUERY_KEY, updatedAsset.id], updatedAsset);
      queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY] });
    },
  });
};

export const useDeleteAsset = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (assetId: string): Promise<void> => {
      await apiClient.request(`/api/product.serviceops/assetmanager/assets/${assetId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: (_, assetId) => {
      queryClient.removeQueries({ queryKey: [ASSETS_QUERY_KEY, assetId] });
      queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY] });
    },
  });
};

// Asset Maintenance hooks
export const useAssetMaintenance = (assetId?: string) => {
  return useQuery({
    queryKey: [ASSET_MAINTENANCE_QUERY_KEY, assetId],
    queryFn: async (): Promise<AssetMaintenance[]> => {
      const endpoint = assetId 
        ? `/api/product.serviceops/assetmanager/assets/${assetId}/maintenance` 
        : '/api/product.serviceops/assetmanager/asset-maintenance';
      const response = await apiClient.request<AssetMaintenance[]>(endpoint);
      return response.map(record => assetMaintenanceSchema.parse(record));
    },
  });
};

export const useCreateMaintenance = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      assetId, 
      maintenance 
    }: { 
      assetId: string; 
      maintenance: AssetMaintenanceFormValues 
    }): Promise<AssetMaintenance> => {
      const response = await apiClient.request<AssetMaintenance>(
        `/api/product.serviceops/assetmanager/assets/${assetId}/maintenance`, 
        {
          method: 'POST',
          body: JSON.stringify(maintenance),
        }
      );
      return assetMaintenanceSchema.parse(response);
    },
    onSuccess: (_, { assetId }) => {
      queryClient.invalidateQueries({ queryKey: [ASSET_MAINTENANCE_QUERY_KEY, assetId] });
      queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY, assetId] });
    },
  });
};

export const useUpdateMaintenance = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      maintenanceId, 
      maintenance 
    }: { 
      maintenanceId: string; 
      maintenance: Partial<AssetMaintenanceFormValues> 
    }): Promise<AssetMaintenance> => {
      const response = await apiClient.request<AssetMaintenance>(
        `/api/product.serviceops/assetmanager/asset-maintenance/${maintenanceId}`, 
        {
          method: 'PUT',
          body: JSON.stringify(maintenance),
        }
      );
      return assetMaintenanceSchema.parse(response);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ASSET_MAINTENANCE_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY] });
    },
  });
};

// Asset Assignment hooks
export const useAssetAssignments = (assetId?: string) => {
  return useQuery({
    queryKey: [ASSET_ASSIGNMENTS_QUERY_KEY, assetId],
    queryFn: async (): Promise<AssetAssignment[]> => {
      const endpoint = assetId 
        ? `/api/product.serviceops/assetmanager/assets/${assetId}/assignments` 
        : '/api/product.serviceops/assetmanager/asset-assignments';
      const response = await apiClient.request<AssetAssignment[]>(endpoint);
      return response.map(assignment => assetAssignmentSchema.parse(assignment));
    },
  });
};

export const useAssignAsset = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      assetId, 
      assignment 
    }: { 
      assetId: string; 
      assignment: AssetAssignmentFormValues 
    }): Promise<AssetAssignment> => {
      const response = await apiClient.request<AssetAssignment>(
        `/api/product.serviceops/assetmanager/asset/${assetId}/issue`, 
        {
          method: 'POST',
          body: JSON.stringify(assignment),
        }
      );
      return assetAssignmentSchema.parse(response);
    },
    onSuccess: (_, { assetId }) => {
      queryClient.invalidateQueries({ queryKey: [ASSET_ASSIGNMENTS_QUERY_KEY, assetId] });
      queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY, assetId] });
    },
  });
};

export const useReturnAsset = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      assignmentId, 
      notes 
    }: { 
      assignmentId: string; 
      notes?: string 
    }): Promise<AssetAssignment> => {
      const response = await apiClient.request<AssetAssignment>(
        `/api/product.serviceops/assetmanager/asset-assignments/${assignmentId}/return`, 
        {
          method: 'POST',
          body: JSON.stringify({ notes }),
        }
      );
      return assetAssignmentSchema.parse(response);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ASSET_ASSIGNMENTS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY] });
    },
  });
};

// Asset Audit hooks
export const useAssetAudits = () => {
  return useQuery({
    queryKey: [ASSET_AUDITS_QUERY_KEY],
    queryFn: async (): Promise<AssetAudit[]> => {
      const response = await apiClient.request<AssetAudit[]>('/api/product.serviceops/assetmanager/asset-audits');
      return response.map(audit => assetAuditSchema.parse(audit));
    },
  });
};

export const useCreateAudit = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (audit: Omit<AssetAudit, 'id' | 'createdBy' | 'createdAt' | 'updatedAt'>): Promise<AssetAudit> => {
      const response = await apiClient.request<AssetAudit>('/api/product.serviceops/assetmanager/asset-audits', {
        method: 'POST',
        body: JSON.stringify(audit),
      });
      return assetAuditSchema.parse(response);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ASSET_AUDITS_QUERY_KEY] });
    },
  });
};

// Bulk Operations
export const useBulkUpdateAssets = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      assetIds, 
      updates 
    }: { 
      assetIds: string[]; 
      updates: Partial<AssetFormValues> 
    }): Promise<Asset[]> => {
      const response = await apiClient.request<Asset[]>('/api/product.serviceops/assetmanager/assets/bulk-update', {
        method: 'POST',
        body: JSON.stringify({ assetIds, updates }),
      });
      
      return response.map(asset => assetSchema.parse(asset));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ASSETS_QUERY_KEY] });
    },
  });
};

// Export functionality
export const useExportAssets = () => {
  return useMutation({
    mutationFn: async (searchParams?: AssetSearchParams): Promise<Blob> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await fetch(`/api/product.serviceops/assetmanager/assets/export?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-AUTH-TOKEN': localStorage.getItem('authToken') || '',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Export failed: ${response.status}`);
      }
      
      return response.blob();
    },
  });
};

// Asset Depreciation
export const useCalculateDepreciation = () => {
  return useMutation({
    mutationFn: async (assetId: string): Promise<{ currentValue: number; depreciationAmount: number }> => {
      const response = await apiClient.request<{ currentValue: number; depreciationAmount: number }>(
        `/api/product.serviceops/assetmanager/assets/${assetId}/depreciation`
      );
      return response;
    },
  });
};