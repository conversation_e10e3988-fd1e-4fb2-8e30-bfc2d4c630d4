import { ReactNode } from 'react';
import { usePermissions } from '../hooks/usePermissions';
import { Permission } from '../schemas/authSchemas';

interface RoleGuardProps {
  children: ReactNode;
  permissions?: Permission[];
  roles?: string[];
  requireAll?: boolean;
  fallback?: ReactNode;
}

export function RoleGuard({
  children,
  permissions = [],
  roles = [],
  requireAll = false,
  fallback = null,
}: RoleGuardProps) {
  const { hasAnyPermission, hasAllPermissions, hasRole } = usePermissions();

  // Check permissions
  const hasRequiredPermissions = () => {
    if (permissions.length === 0) return true;
    
    if (requireAll) {
      return hasAllPermissions(permissions);
    } else {
      return hasAnyPermission(permissions);
    }
  };

  // Check roles
  const hasRequiredRoles = () => {
    if (roles.length === 0) return true;
    
    if (requireAll) {
      return roles.every(role => hasRole(role));
    } else {
      return roles.some(role => hasRole(role));
    }
  };

  // User must have both required permissions AND roles
  const hasAccess = hasRequiredPermissions() && hasRequiredRoles();

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}