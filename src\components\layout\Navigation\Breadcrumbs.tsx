import { useLocation, Link } from 'react-router';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { ChevronRight, Home } from 'lucide-react';

// Route label mapping
const routeLabels: Record<string, string> = {
  // Root
  dashboard: 'Dashboard',
  
  // CustomerOps
  customerops: 'CustomerOps',
  customers: 'Customers',
  leads: 'Leads',
  deals: 'Deals',
  quotes: 'Quotes',
  
  // ServiceOps
  serviceops: 'ServiceOps',
  assets: 'Assets',
  tickets: 'Tickets',
  assignments: 'Assignments',
  
  // Timesheets
  timesheets: 'Timesheets',
  team: 'Team',
  reports: 'Reports',
  
  // AccountOps
  accountops: 'AccountOps',
  invoices: 'Invoices',
  expenses: 'Expenses',
  budgets: 'Budgets',
  
  // System
  system: 'System',
  users: 'Users',
  roles: 'Roles',
  entities: 'Entities',
  settings: 'Settings',
};

export function Breadcrumbs() {
  const location = useLocation();
  
  const pathnames = location.pathname.split('/').filter(Boolean);
  
  // Don't show breadcrumbs on home page
  if (pathnames.length === 0) {
    return null;
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {/* Always show home */}
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link to="/dashboard" className="flex items-center gap-1">
              <Home className="h-4 w-4" />
              Home
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        
        {pathnames.map((pathname, index) => {
          const routeTo = `/${pathnames.slice(0, index + 1).join('/')}`;
          const isLast = index === pathnames.length - 1;
          const label = routeLabels[pathname] || pathname;
          
          return (
            <div key={pathname} className="flex items-center gap-2">
              <BreadcrumbSeparator>
                <ChevronRight className="h-4 w-4" />
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                {isLast ? (
                  <BreadcrumbPage className="capitalize">
                    {label}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link to={routeTo} className="capitalize">
                      {label}
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </div>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}