import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import {
  Ticket,
  TicketFormValues,
  TicketSearchParams,
  TicketListResponse,
  TicketStats,
  TicketComment,
  TicketCommentFormValues,
  TicketTimeEntry,
  TicketTimeEntryFormValues,
  SLAConfig,
  KnowledgeBaseArticle,
  ticketListResponseSchema,
  ticketSchema,
  ticketStatsSchema,
  ticketCommentSchema,
  ticketTimeEntrySchema,
  slaConfigSchema,
  knowledgeBaseArticleSchema,
} from '../schemas/ticketSchemas';

const TICKETS_QUERY_KEY = 'tickets';
const TICKET_COMMENTS_QUERY_KEY = 'ticket-comments';
const TICKET_TIME_ENTRIES_QUERY_KEY = 'ticket-time-entries';
const SLA_CONFIGS_QUERY_KEY = 'sla-configs';
const KNOWLEDGE_BASE_QUERY_KEY = 'knowledge-base';

// Ticket Query hooks
export const useTickets = (searchParams?: TicketSearchParams) => {
  return useQuery({
    queryKey: [TICKETS_QUERY_KEY, searchParams],
    queryFn: async (): Promise<TicketListResponse> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await apiClient.request<TicketListResponse>(
        `/api/product.serviceops/ticket/search?${params.toString()}`
      );
      
      return ticketListResponseSchema.parse(response);
    },
  });
};

export const useTicket = (ticketId: string) => {
  return useQuery({
    queryKey: [TICKETS_QUERY_KEY, ticketId],
    queryFn: async (): Promise<Ticket> => {
      const response = await apiClient.request<Ticket>(
        `/api/product.serviceops/tickets/${ticketId}`
      );
      return ticketSchema.parse(response);
    },
    enabled: !!ticketId,
  });
};

export const useTicketStats = (filters?: Partial<TicketSearchParams>) => {
  return useQuery({
    queryKey: [TICKETS_QUERY_KEY, 'stats', filters],
    queryFn: async (): Promise<TicketStats> => {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });
      }
      
      const response = await apiClient.request<TicketStats>(
        `/api/product.serviceops/tickets/stats?${params.toString()}`
      );
      
      return ticketStatsSchema.parse(response);
    },
  });
};

// Ticket Mutation hooks
export const useCreateTicket = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (ticket: TicketFormValues): Promise<Ticket> => {
      const response = await apiClient.request<Ticket>('/api/product.serviceops/ticket', {
        method: 'POST',
        body: JSON.stringify(ticket),
      });
      return ticketSchema.parse(response);
    },
    onSuccess: (newTicket) => {
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY] });
      queryClient.setQueryData([TICKETS_QUERY_KEY, newTicket.id], newTicket);
    },
  });
};

export const useUpdateTicket = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      ticketId, 
      ticket 
    }: { 
      ticketId: string; 
      ticket: Partial<TicketFormValues> 
    }): Promise<Ticket> => {
      const response = await apiClient.request<Ticket>(`/api/product.serviceops/tickets/${ticketId}`, {
        method: 'PUT',
        body: JSON.stringify(ticket),
      });
      return ticketSchema.parse(response);
    },
    onSuccess: (updatedTicket) => {
      queryClient.setQueryData([TICKETS_QUERY_KEY, updatedTicket.id], updatedTicket);
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY] });
    },
  });
};

export const useDeleteTicket = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (ticketId: string): Promise<void> => {
      await apiClient.request(`/api/product.serviceops/tickets/${ticketId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: (_, ticketId) => {
      queryClient.removeQueries({ queryKey: [TICKETS_QUERY_KEY, ticketId] });
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY] });
    },
  });
};

// Ticket Status Updates
export const useAssignTicket = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      ticketId, 
      assignedToUserId, 
      assignedToUserName 
    }: { 
      ticketId: string; 
      assignedToUserId: string; 
      assignedToUserName: string; 
    }): Promise<Ticket> => {
      const response = await apiClient.request<Ticket>(`/api/product.serviceops/tickets/${ticketId}/assign`, {
        method: 'POST',
        body: JSON.stringify({ assignedToUserId, assignedToUserName }),
      });
      return ticketSchema.parse(response);
    },
    onSuccess: (updatedTicket) => {
      queryClient.setQueryData([TICKETS_QUERY_KEY, updatedTicket.id], updatedTicket);
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY] });
    },
  });
};

export const useResolveTicket = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      ticketId, 
      resolution, 
      resolutionCategory 
    }: { 
      ticketId: string; 
      resolution: string; 
      resolutionCategory?: string; 
    }): Promise<Ticket> => {
      const response = await apiClient.request<Ticket>(`/api/product.serviceops/tickets/${ticketId}/resolve`, {
        method: 'POST',
        body: JSON.stringify({ resolution, resolutionCategory }),
      });
      return ticketSchema.parse(response);
    },
    onSuccess: (updatedTicket) => {
      queryClient.setQueryData([TICKETS_QUERY_KEY, updatedTicket.id], updatedTicket);
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY] });
    },
  });
};

export const useCloseTicket = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      ticketId, 
      closeReason 
    }: { 
      ticketId: string; 
      closeReason?: string; 
    }): Promise<Ticket> => {
      const response = await apiClient.request<Ticket>(`/api/product.serviceops/tickets/${ticketId}/close`, {
        method: 'POST',
        body: JSON.stringify({ closeReason }),
      });
      return ticketSchema.parse(response);
    },
    onSuccess: (updatedTicket) => {
      queryClient.setQueryData([TICKETS_QUERY_KEY, updatedTicket.id], updatedTicket);
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY] });
    },
  });
};

export const useEscalateTicket = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      ticketId, 
      escalatedToUserId, 
      escalatedToUserName, 
      reason 
    }: { 
      ticketId: string; 
      escalatedToUserId: string; 
      escalatedToUserName: string; 
      reason?: string; 
    }): Promise<Ticket> => {
      const response = await apiClient.request<Ticket>(`/api/product.serviceops/tickets/${ticketId}/escalate`, {
        method: 'POST',
        body: JSON.stringify({ escalatedToUserId, escalatedToUserName, reason }),
      });
      return ticketSchema.parse(response);
    },
    onSuccess: (updatedTicket) => {
      queryClient.setQueryData([TICKETS_QUERY_KEY, updatedTicket.id], updatedTicket);
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY] });
    },
  });
};

// Ticket Comments
export const useTicketComments = (ticketId?: string) => {
  return useQuery({
    queryKey: [TICKET_COMMENTS_QUERY_KEY, ticketId],
    queryFn: async (): Promise<TicketComment[]> => {
      const endpoint = ticketId 
        ? `/api/product.serviceops/tickets/${ticketId}/comments` 
        : '/api/product.serviceops/ticket-comments';
      const response = await apiClient.request<TicketComment[]>(endpoint);
      return response.map(comment => ticketCommentSchema.parse(comment));
    },
    enabled: !!ticketId,
  });
};

export const useAddTicketComment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      ticketId, 
      comment 
    }: { 
      ticketId: string; 
      comment: TicketCommentFormValues 
    }): Promise<TicketComment> => {
      const response = await apiClient.request<TicketComment>(
        `/api/product.serviceops/tickets/${ticketId}/comments`, 
        {
          method: 'POST',
          body: JSON.stringify(comment),
        }
      );
      return ticketCommentSchema.parse(response);
    },
    onSuccess: (_, { ticketId }) => {
      queryClient.invalidateQueries({ queryKey: [TICKET_COMMENTS_QUERY_KEY, ticketId] });
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY, ticketId] });
    },
  });
};

// Ticket Time Entries
export const useTicketTimeEntries = (ticketId?: string) => {
  return useQuery({
    queryKey: [TICKET_TIME_ENTRIES_QUERY_KEY, ticketId],
    queryFn: async (): Promise<TicketTimeEntry[]> => {
      const endpoint = ticketId 
        ? `/api/product.serviceops/tickets/${ticketId}/time-entries` 
        : '/api/product.serviceops/ticket-time-entries';
      const response = await apiClient.request<TicketTimeEntry[]>(endpoint);
      return response.map(entry => ticketTimeEntrySchema.parse(entry));
    },
    enabled: !!ticketId,
  });
};

export const useAddTicketTimeEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      ticketId, 
      timeEntry 
    }: { 
      ticketId: string; 
      timeEntry: TicketTimeEntryFormValues 
    }): Promise<TicketTimeEntry> => {
      const response = await apiClient.request<TicketTimeEntry>(
        `/api/product.serviceops/tickets/${ticketId}/time-entries`, 
        {
          method: 'POST',
          body: JSON.stringify(timeEntry),
        }
      );
      return ticketTimeEntrySchema.parse(response);
    },
    onSuccess: (_, { ticketId }) => {
      queryClient.invalidateQueries({ queryKey: [TICKET_TIME_ENTRIES_QUERY_KEY, ticketId] });
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY, ticketId] });
    },
  });
};

// SLA Configuration
export const useSLAConfigs = () => {
  return useQuery({
    queryKey: [SLA_CONFIGS_QUERY_KEY],
    queryFn: async (): Promise<SLAConfig[]> => {
      const response = await apiClient.request<SLAConfig[]>('/api/product.serviceops/sla-configs');
      return response.map(config => slaConfigSchema.parse(config));
    },
  });
};

export const useCreateSLAConfig = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (config: Omit<SLAConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<SLAConfig> => {
      const response = await apiClient.request<SLAConfig>('/api/product.serviceops/sla-configs', {
        method: 'POST',
        body: JSON.stringify(config),
      });
      return slaConfigSchema.parse(response);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [SLA_CONFIGS_QUERY_KEY] });
    },
  });
};

// Knowledge Base
export const useKnowledgeBaseArticles = (category?: string) => {
  return useQuery({
    queryKey: [KNOWLEDGE_BASE_QUERY_KEY, category],
    queryFn: async (): Promise<KnowledgeBaseArticle[]> => {
      const params = new URLSearchParams();
      if (category) {
        params.append('category', category);
      }
      
      const response = await apiClient.request<KnowledgeBaseArticle[]>(
        `/api/product.serviceops/knowledge-base?${params.toString()}`
      );
      return response.map(article => knowledgeBaseArticleSchema.parse(article));
    },
  });
};

export const useCreateKnowledgeBaseArticle = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (article: Omit<KnowledgeBaseArticle, 'id' | 'viewCount' | 'helpfulCount' | 'notHelpfulCount' | 'createdAt' | 'updatedAt'>): Promise<KnowledgeBaseArticle> => {
      const response = await apiClient.request<KnowledgeBaseArticle>('/api/product.serviceops/knowledge-base', {
        method: 'POST',
        body: JSON.stringify(article),
      });
      return knowledgeBaseArticleSchema.parse(response);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [KNOWLEDGE_BASE_QUERY_KEY] });
    },
  });
};

// Bulk Operations
export const useBulkUpdateTickets = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      ticketIds, 
      updates 
    }: { 
      ticketIds: string[]; 
      updates: Partial<TicketFormValues> 
    }): Promise<Ticket[]> => {
      const response = await apiClient.request<Ticket[]>('/api/product.serviceops/tickets/bulk-update', {
        method: 'POST',
        body: JSON.stringify({ ticketIds, updates }),
      });
      
      return response.map(ticket => ticketSchema.parse(ticket));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY] });
    },
  });
};

// Export functionality
export const useExportTickets = () => {
  return useMutation({
    mutationFn: async (searchParams?: TicketSearchParams): Promise<Blob> => {
      const params = new URLSearchParams();
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }
      
      const response = await fetch(`/api/product.serviceops/tickets/export?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-AUTH-TOKEN': localStorage.getItem('authToken') || '',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Export failed: ${response.status}`);
      }
      
      return response.blob();
    },
  });
};

// Satisfaction Survey
export const useSubmitSatisfactionSurvey = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      ticketId, 
      rating, 
      comment 
    }: { 
      ticketId: string; 
      rating: number; 
      comment?: string; 
    }): Promise<Ticket> => {
      const response = await apiClient.request<Ticket>(`/api/product.serviceops/tickets/${ticketId}/satisfaction`, {
        method: 'POST',
        body: JSON.stringify({ rating, comment }),
      });
      return ticketSchema.parse(response);
    },
    onSuccess: (updatedTicket) => {
      queryClient.setQueryData([TICKETS_QUERY_KEY, updatedTicket.id], updatedTicket);
      queryClient.invalidateQueries({ queryKey: [TICKETS_QUERY_KEY] });
    },
  });
};