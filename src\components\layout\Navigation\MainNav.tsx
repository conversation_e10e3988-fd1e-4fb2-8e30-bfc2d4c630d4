import { Link, useLocation } from 'react-router';
import { cn } from '@/lib/utils';
import { usePermissions } from '@/features/auth/hooks/usePermissions';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { 
  Users, 
  Settings, 
  Clock, 
  DollarSign, 
  Wrench,
  BarChart3,
  Building,
  UserCheck,
  TrendingUp,
  ShoppingCart,
  Package,
  Ticket,
  Timer,
  FileText,
  PieChart
} from 'lucide-react';

const moduleConfig = [
  {
    id: 'customerops',
    title: 'CustomerOps',
    description: 'Customer & lead management',
    icon: Users,
    permission: 'customerops:read',
    items: [
      { title: 'Dashboard', href: '/customerops', icon: BarChart3 },
      { title: 'Customers', href: '/customerops/customers', icon: Building },
      { title: 'Leads', href: '/customerops/leads', icon: User<PERSON>he<PERSON> },
      { title: 'Deals', href: '/customerops/deals', icon: TrendingUp },
      { title: 'Quotes', href: '/customerops/quotes', icon: ShoppingCart },
    ]
  },
  {
    id: 'serviceops',
    title: 'ServiceOps',
    description: 'Asset & ticket management',
    icon: Wrench,
    permission: 'serviceops:read',
    items: [
      { title: 'Dashboard', href: '/serviceops', icon: BarChart3 },
      { title: 'Assets', href: '/serviceops/assets', icon: Package },
      { title: 'Tickets', href: '/serviceops/tickets', icon: Ticket },
      { title: 'Assignments', href: '/serviceops/assignments', icon: UserCheck },
    ]
  },
  {
    id: 'timesheets',
    title: 'Timesheets',
    description: 'Time tracking & management',
    icon: Clock,
    permission: 'timesheets:read',
    items: [
      { title: 'My Timesheet', href: '/timesheets', icon: Timer },
      { title: 'Team Timesheets', href: '/timesheets/team', icon: Users },
      { title: 'Reports', href: '/timesheets/reports', icon: FileText },
    ]
  },
  {
    id: 'accountops',
    title: 'AccountOps',
    description: 'Financial management',
    icon: DollarSign,
    permission: 'accountops:read',
    items: [
      { title: 'Dashboard', href: '/accountops', icon: PieChart },
      { title: 'Invoices', href: '/accountops/invoices', icon: FileText },
      { title: 'Expenses', href: '/accountops/expenses', icon: DollarSign },
      { title: 'Budgets', href: '/accountops/budgets', icon: TrendingUp },
    ]
  },
  {
    id: 'system',
    title: 'System',
    description: 'Administration & settings',
    icon: Settings,
    permission: 'system:admin',
    items: [
      { title: 'Users', href: '/system/users', icon: Users },
      { title: 'Roles', href: '/system/roles', icon: UserCheck },
      { title: 'Entities', href: '/system/entities', icon: Building },
      { title: 'Settings', href: '/system/settings', icon: Settings },
    ]
  }
];

export function MainNav() {
  const location = useLocation();
  const { canAccessModule } = usePermissions();

  const visibleModules = moduleConfig.filter(module => 
    canAccessModule(module.id)
  );

  return (
    <NavigationMenu className="hidden md:flex">
      <NavigationMenuList>
        {visibleModules.map((module) => (
          <NavigationMenuItem key={module.id}>
            <NavigationMenuTrigger className="flex items-center gap-2">
              <module.icon className="h-4 w-4" />
              {module.title}
            </NavigationMenuTrigger>
            <NavigationMenuContent>
              <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px]">
                <li className="row-span-3">
                  <NavigationMenuLink asChild>
                    <Link
                      className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                      to={`/${module.id}`}
                    >
                      <module.icon className="h-6 w-6" />
                      <div className="mb-2 mt-4 text-lg font-medium">
                        {module.title}
                      </div>
                      <p className="text-sm leading-tight text-muted-foreground">
                        {module.description}
                      </p>
                    </Link>
                  </NavigationMenuLink>
                </li>
                {module.items.map((item) => (
                  <li key={item.href}>
                    <NavigationMenuLink asChild>
                      <Link
                        to={item.href}
                        className={cn(
                          "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
                          location.pathname === item.href && "bg-accent text-accent-foreground"
                        )}
                      >
                        <div className="flex items-center gap-2">
                          <item.icon className="h-4 w-4" />
                          <div className="text-sm font-medium leading-none">
                            {item.title}
                          </div>
                        </div>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                ))}
              </ul>
            </NavigationMenuContent>
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
}