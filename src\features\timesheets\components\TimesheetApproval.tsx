import { useState } from 'react';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  User, 
  Calendar,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { 
  useTimesheets, 
  useApproveTimesheet, 
  useRejectTimesheet 
} from '../hooks/useTimesheets';
import { Timesheet, TimesheetStatus } from '../schemas/timesheetSchemas';

interface TimesheetApprovalProps {
  entityId?: string;
}

export function TimesheetApproval({}: TimesheetApprovalProps) {
  const [selectedTimesheet, setSelectedTimesheet] = useState<Timesheet | null>(null);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [approvalComment, setApprovalComment] = useState('');
  const [expandedTimesheets, setExpandedTimesheets] = useState<Set<string>>(new Set());

  const { data: timesheetsData, isLoading } = useTimesheets({
    status: 'submitted',
    approvalStatus: 'pending',
    limit: 50,
    page: 1,
    sortBy: 'date',
    sortOrder: 'desc',
  });

  const approveTimesheet = useApproveTimesheet();
  const rejectTimesheet = useRejectTimesheet();

  const timesheets = timesheetsData?.timesheets || [];

  const toggleExpanded = (timesheetId: string) => {
    const newExpanded = new Set(expandedTimesheets);
    if (newExpanded.has(timesheetId)) {
      newExpanded.delete(timesheetId);
    } else {
      newExpanded.add(timesheetId);
    }
    setExpandedTimesheets(newExpanded);
  };

  const handleApprove = (timesheet: Timesheet) => {
    setSelectedTimesheet(timesheet);
    setApprovalComment('');
    setApprovalDialogOpen(true);
  };

  const handleReject = (timesheet: Timesheet) => {
    setSelectedTimesheet(timesheet);
    setRejectionReason('');
    setRejectionDialogOpen(true);
  };

  const handleApprovalSubmit = async () => {
    if (!selectedTimesheet) return;

    try {
      await approveTimesheet.mutateAsync({
        timesheetId: selectedTimesheet.id,
        comment: approvalComment.trim() || undefined,
      });
      setApprovalDialogOpen(false);
      setSelectedTimesheet(null);
      setApprovalComment('');
    } catch (error) {
      console.error('Failed to approve timesheet:', error);
    }
  };

  const handleRejectionSubmit = async () => {
    if (!selectedTimesheet || !rejectionReason.trim()) return;

    try {
      await rejectTimesheet.mutateAsync({
        timesheetId: selectedTimesheet.id,
        reason: rejectionReason.trim(),
      });
      setRejectionDialogOpen(false);
      setSelectedTimesheet(null);
      setRejectionReason('');
    } catch (error) {
      console.error('Failed to reject timesheet:', error);
    }
  };

  const getStatusColor = (status: TimesheetStatus) => {
    const colors = {
      draft: 'bg-gray-100 text-gray-800',
      submitted: 'bg-blue-100 text-blue-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      pending_review: 'bg-yellow-100 text-yellow-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatHours = (hours: number) => {
    return hours.toFixed(2);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-64 mb-4"></div>
          <div className="h-96 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Timesheet Approval</h2>
          <p className="text-muted-foreground">
            Review and approve submitted timesheets
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            {timesheets.length} Pending
          </Badge>
        </div>
      </div>

      {/* Pending Timesheets */}
      {timesheets.length === 0 ? (
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-muted-foreground">
              <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg">No timesheets pending approval</p>
              <p className="text-sm">All submitted timesheets have been reviewed.</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {timesheets.map((timesheet) => {
            const isExpanded = expandedTimesheets.has(timesheet.id);
            const weekStart = new Date(timesheet.weekStartDate);
            const weekEnd = new Date(timesheet.weekEndDate);
            
            return (
              <Card key={timesheet.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div>
                        <CardTitle className="text-lg">{timesheet.userName}</CardTitle>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                          <Calendar className="h-4 w-4" />
                          <span>
                            {weekStart.toLocaleDateString()} - {weekEnd.toLocaleDateString()}
                          </span>
                          <Badge variant="outline" className={getStatusColor(timesheet.status)}>
                            {timesheet.status.replace('_', ' ')}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="text-2xl font-bold">
                          {formatHours(timesheet.totalHours)}h
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {formatCurrency(timesheet.totalAmount)}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <RoleGuard permissions={[PERMISSIONS['timesheets:approve']]}>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-green-600 hover:text-green-700"
                            onClick={() => handleApprove(timesheet)}
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => handleReject(timesheet)}
                          >
                            <XCircle className="h-4 w-4 mr-1" />
                            Reject
                          </Button>
                        </RoleGuard>
                        
                        <Collapsible open={isExpanded} onOpenChange={() => toggleExpanded(timesheet.id)}>
                          <CollapsibleTrigger asChild>
                            <Button variant="ghost" size="sm">
                              {isExpanded ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )}
                            </Button>
                          </CollapsibleTrigger>
                        </Collapsible>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <Collapsible open={isExpanded} onOpenChange={() => toggleExpanded(timesheet.id)}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      {/* Summary Stats */}
                      <div className="grid grid-cols-4 gap-4 mb-6">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {timesheet.entries.length}
                          </div>
                          <div className="text-xs text-muted-foreground">Entries</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {formatHours(timesheet.totalBillableHours)}h
                          </div>
                          <div className="text-xs text-muted-foreground">Billable</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {formatHours(timesheet.totalHours - timesheet.totalBillableHours)}h
                          </div>
                          <div className="text-xs text-muted-foreground">Non-billable</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {timesheet.entries.filter(e => e.type === 'overtime').length}
                          </div>
                          <div className="text-xs text-muted-foreground">Overtime</div>
                        </div>
                      </div>

                      {/* Time Entries Table */}
                      <div className="border rounded-lg">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date</TableHead>
                              <TableHead>Description</TableHead>
                              <TableHead>Project</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Hours</TableHead>
                              <TableHead>Rate</TableHead>
                              <TableHead>Amount</TableHead>
                              <TableHead>Billable</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {timesheet.entries.map((entry) => (
                              <TableRow key={entry.id}>
                                <TableCell>
                                  {formatDate(entry.date)}
                                </TableCell>
                                <TableCell>
                                  <div>
                                    <div className="font-medium">{entry.description}</div>
                                    {entry.notes && (
                                      <div className="text-xs text-muted-foreground">
                                        {entry.notes}
                                      </div>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {entry.projectName ? (
                                    <div>
                                      <div className="font-medium">{entry.projectName}</div>
                                      {entry.taskName && (
                                        <div className="text-xs text-muted-foreground">
                                          {entry.taskName}
                                        </div>
                                      )}
                                    </div>
                                  ) : (
                                    <span className="text-muted-foreground">No project</span>
                                  )}
                                </TableCell>
                                <TableCell>
                                  <Badge 
                                    variant="outline" 
                                    className={`capitalize ${
                                      entry.type === 'overtime' ? 'bg-orange-50 text-orange-700' : ''
                                    }`}
                                  >
                                    {entry.type.replace('_', ' ')}
                                  </Badge>
                                </TableCell>
                                <TableCell className="font-mono">
                                  {formatHours(entry.duration)}
                                </TableCell>
                                <TableCell className="font-mono">
                                  {entry.hourlyRate ? formatCurrency(entry.hourlyRate) : '-'}
                                </TableCell>
                                <TableCell className="font-mono">
                                  {entry.totalAmount ? formatCurrency(entry.totalAmount) : '-'}
                                </TableCell>
                                <TableCell>
                                  <Badge 
                                    variant="outline" 
                                    className={
                                      entry.billable 
                                        ? 'bg-green-50 text-green-700' 
                                        : 'bg-gray-50 text-gray-700'
                                    }
                                  >
                                    {entry.billable ? 'Yes' : 'No'}
                                  </Badge>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>

                      {/* Submission Details */}
                      <div className="mt-4 p-4 bg-muted/30 rounded-lg">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <User className="h-4 w-4" />
                          <span>
                            Submitted by {timesheet.submittedByUserName} on{' '}
                            {timesheet.submittedAt && formatDate(timesheet.submittedAt)}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>
      )}

      {/* Approval Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Timesheet</DialogTitle>
            <DialogDescription>
              Approve timesheet for {selectedTimesheet?.userName} for the week of{' '}
              {selectedTimesheet && formatDate(selectedTimesheet.weekStartDate)}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="approval-comment">Comment (Optional)</Label>
              <Textarea
                id="approval-comment"
                placeholder="Add any comments about the approval..."
                value={approvalComment}
                onChange={(e) => setApprovalComment(e.target.value)}
                className="mt-1"
              />
            </div>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setApprovalDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleApprovalSubmit}
              disabled={approveTimesheet.isPending}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              {approveTimesheet.isPending ? 'Approving...' : 'Approve'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog open={rejectionDialogOpen} onOpenChange={setRejectionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Timesheet</DialogTitle>
            <DialogDescription>
              Reject timesheet for {selectedTimesheet?.userName} for the week of{' '}
              {selectedTimesheet && formatDate(selectedTimesheet.weekStartDate)}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="rejection-reason">Reason for Rejection *</Label>
              <Textarea
                id="rejection-reason"
                placeholder="Please provide a reason for rejecting this timesheet..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="mt-1"
                required
              />
              <div className="text-xs text-muted-foreground mt-1">
                This reason will be sent to the employee
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setRejectionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={handleRejectionSubmit}
              disabled={!rejectionReason.trim() || rejectTimesheet.isPending}
            >
              <XCircle className="h-4 w-4 mr-2" />
              {rejectTimesheet.isPending ? 'Rejecting...' : 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}