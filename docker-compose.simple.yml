version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: concentric_frontend
    restart: unless-stopped
    ports:
      - '3000:5173'
    volumes:
      - .:/app:cached
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://host.docker.internal:8080
    extra_hosts:
      - "host.docker.internal:host-gateway"