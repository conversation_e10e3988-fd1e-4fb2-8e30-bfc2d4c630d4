import { useState } from 'react';
import { 
  Clock, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  Pa<PERSON>, 
  CheckCircle,
  BarChart3,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { useTimesheetStats, useActiveTimerSession } from '../hooks/useTimesheets';
import { Timer } from './Timer';
import { TimesheetWeekView } from './TimesheetWeekView';
import { TimesheetApproval } from './TimesheetApproval';

interface TimesheetDashboardProps {
  userId: string;
  entityId: string;
  userRole?: string;
}

export function TimesheetDashboard({ userId, entityId, userRole }: TimesheetDashboardProps) {
  const [currentWeekStart, setCurrentWeekStart] = useState(() => {
    const today = new Date();
    const monday = new Date(today);
    monday.setDate(today.getDate() - today.getDay() + 1); // Get Monday of current week
    return monday.toISOString().split('T')[0];
  });

  const { data: stats } = useTimesheetStats({ 
    userId,
    dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Last 30 days
  });

  const { data: activeTimer } = useActiveTimerSession(userId);

  const formatHours = (hours: number) => {
    return hours.toFixed(1);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const canApprove = userRole === 'manager' || userRole === 'admin';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Timesheets</h1>
          <p className="text-lg text-muted-foreground">
            Track time, manage projects, and submit timesheets
          </p>
        </div>
        
        {/* Active Timer Status */}
        {activeTimer && activeTimer.status !== 'stopped' && (
          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                {activeTimer.status === 'running' ? (
                  <div className="flex items-center gap-2 text-green-700">
                    <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
                    <span className="font-medium">Timer Running</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-yellow-700">
                    <Pause className="h-4 w-4" />
                    <span className="font-medium">Timer Paused</span>
                  </div>
                )}
                <div className="text-sm text-muted-foreground">
                  {activeTimer.description}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Total Hours (30d)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatHours(stats.totalHours)}</div>
              <p className="text-xs text-muted-foreground">
                {formatHours(stats.averageHoursPerDay)}/day average
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Billable Hours
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatHours(stats.totalBillableHours)}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(stats.totalAmount)} earned
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                This Week
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatHours(stats.averageHoursPerWeek)}</div>
              <p className="text-xs text-muted-foreground">Hours logged</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Approval Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.approvalStats.approved}</div>
              <p className="text-xs text-muted-foreground">
                {stats.approvalStats.pending} pending approval
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="timesheet" className="space-y-4">
        <TabsList>
          <TabsTrigger value="timesheet">My Timesheet</TabsTrigger>
          <TabsTrigger value="timer">Timer</TabsTrigger>
          {canApprove && (
            <RoleGuard permissions={[PERMISSIONS['timesheets:approve']]}>
              <TabsTrigger value="approval">Approvals</TabsTrigger>
            </RoleGuard>
          )}
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="timesheet" className="space-y-4">
          <TimesheetWeekView
            userId={userId}
            entityId={entityId}
            weekStartDate={currentWeekStart}
            onWeekChange={setCurrentWeekStart}
          />
        </TabsContent>

        <TabsContent value="timer" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Timer
              userId={userId}
              entityId={entityId}
              onTimeEntryCreated={() => {
                // Refresh timesheet if on current week
              }}
            />
            
            {/* Recent Time Entries */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Entries
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stats?.trendsLast30Days.slice(-5).reverse().map((day) => (
                    <div key={day.date} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">
                          {new Date(day.date).toLocaleDateString('en-US', { 
                            weekday: 'short', 
                            month: 'short', 
                            day: 'numeric' 
                          })}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {formatHours(day.billableHours)} billable of {formatHours(day.totalHours)} total
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatCurrency(day.amount)}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatHours(day.totalHours)}h
                        </div>
                      </div>
                    </div>
                  )) || (
                    <div className="text-center text-muted-foreground py-8">
                      <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No recent time entries</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {canApprove && (
          <TabsContent value="approval" className="space-y-4">
            <RoleGuard permissions={[PERMISSIONS['timesheets:approve']]}>
              <TimesheetApproval entityId={entityId} />
            </RoleGuard>
          </TabsContent>
        )}

        <TabsContent value="reports" className="space-y-4">
          {/* Time Distribution */}
          {stats && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="h-5 w-5" />
                    Time by Type
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stats.typeBreakdown.map((type) => (
                      <div key={type.type} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-primary" />
                          <span className="capitalize">{type.type.replace('_', ' ')}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{formatHours(type.hours)}h</div>
                          <div className="text-xs text-muted-foreground">
                            {type.percentage.toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Time by Project
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stats.projectBreakdown.slice(0, 5).map((project) => (
                      <div key={project.projectId} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-green-500" />
                          <span className="truncate">{project.projectName}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{formatHours(project.hours)}h</div>
                          <div className="text-xs text-muted-foreground">
                            {formatCurrency(project.amount)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Trends Chart Placeholder */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                30-Day Trend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center border border-dashed rounded-lg">
                <div className="text-center text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg">Time Tracking Chart</p>
                  <p className="text-sm">Chart component would go here</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}