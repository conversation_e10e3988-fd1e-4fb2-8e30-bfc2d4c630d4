import { useState, useEffect } from 'react';
import { 
  CalendarDays, 
  Clock, 
  Briefcase, 
  CheckSquare, 
  Tag, 
  DollarSign,
  Save,
  X
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { useProjects, useTasks, useCreateTimeEntry, useUpdateTimeEntry } from '../hooks/useTimesheets';
import { 
  TimeEntry, 
  TimeEntryFormValues, 
  TimeEntryType
} from '../schemas/timesheetSchemas';

interface SimpleTimeEntryFormProps {
  entityId: string;
  userId: string;
  userName: string;
  userEmail: string;
  entry?: TimeEntry;
  onSubmit?: (entry: TimeEntry) => void;
  onCancel?: () => void;
}

export function SimpleTimeEntryForm({ 
  entityId, 
  userId, 
  userName, 
  userEmail, 
  entry, 
  onSubmit, 
  onCancel 
}: SimpleTimeEntryFormProps) {
  const [formData, setFormData] = useState<Partial<TimeEntryFormValues>>({
    entityId,
    userId,
    userName,
    userEmail,
    date: new Date().toISOString().split('T')[0],
    startTime: '09:00',
    endTime: '17:00',
    duration: 8,
    type: 'regular' as TimeEntryType,
    description: '',
    notes: '',
    billable: true,
    status: 'draft',
    tags: [],
    ...entry,
  });

  const [selectedProjectId, setSelectedProjectId] = useState<string>(entry?.projectId || '');

  const { data: projects } = useProjects(entityId);
  const { data: tasks } = useTasks(selectedProjectId);
  
  const createTimeEntry = useCreateTimeEntry();
  const updateTimeEntry = useUpdateTimeEntry();

  // Calculate duration from start/end times
  useEffect(() => {
    if (formData.startTime && formData.endTime) {
      const start = new Date(`2000-01-01T${formData.startTime}`);
      const end = new Date(`2000-01-01T${formData.endTime}`);
      
      if (end > start) {
        const diffMs = end.getTime() - start.getTime();
        const hours = diffMs / (1000 * 60 * 60);
        setFormData(prev => ({ ...prev, duration: Number(hours.toFixed(2)) }));
      }
    }
  }, [formData.startTime, formData.endTime]);

  // Calculate total amount
  useEffect(() => {
    if (formData.duration && formData.hourlyRate && formData.hourlyRate > 0) {
      const total = formData.duration * formData.hourlyRate;
      setFormData(prev => ({ ...prev, totalAmount: Number(total.toFixed(2)) }));
    }
  }, [formData.duration, formData.hourlyRate]);

  const selectedProject = projects?.find(p => p.id === selectedProjectId);

  // Auto-fill hourly rate from project or task
  useEffect(() => {
    if (!formData.hourlyRate || formData.hourlyRate === 0) {
      const task = tasks?.find(t => t.id === formData.taskId);
      
      if (task?.hourlyRate && task.hourlyRate > 0) {
        setFormData(prev => ({ ...prev, hourlyRate: task.hourlyRate }));
      } else if (selectedProject?.hourlyRate && selectedProject.hourlyRate > 0) {
        setFormData(prev => ({ ...prev, hourlyRate: selectedProject.hourlyRate }));
      }
    }
  }, [selectedProject, tasks, formData.taskId, formData.hourlyRate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const values = formData as TimeEntryFormValues;
      
      if (entry) {
        // Update existing entry
        const updatedEntry = await updateTimeEntry.mutateAsync({
          entryId: entry.id,
          timeEntry: values,
        });
        onSubmit?.(updatedEntry);
      } else {
        // Create new entry
        const newEntry = await createTimeEntry.mutateAsync(values);
        onSubmit?.(newEntry);
      }
    } catch (error) {
      console.error('Failed to save time entry:', error);
    }
  };

  const updateFormData = (key: keyof TimeEntryFormValues, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  const timeEntryTypes: { value: TimeEntryType; label: string }[] = [
    { value: 'regular', label: 'Regular Work' },
    { value: 'overtime', label: 'Overtime' },
    { value: 'break', label: 'Break' },
    { value: 'training', label: 'Training' },
    { value: 'meeting', label: 'Meeting' },
    { value: 'travel', label: 'Travel' },
    { value: 'sick', label: 'Sick Leave' },
    { value: 'vacation', label: 'Vacation' },
    { value: 'personal', label: 'Personal Time' },
    { value: 'holiday', label: 'Holiday' },
  ];

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          {entry ? 'Edit Time Entry' : 'Add Time Entry'}
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date" className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4" />
                Date *
              </Label>
              <Input
                id="date"
                type="date"
                value={formData.date || ''}
                onChange={(e) => updateFormData('date', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Type *</Label>
              <Select 
                value={formData.type || 'regular'} 
                onValueChange={(value) => updateFormData('type', value as TimeEntryType)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {timeEntryTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Time Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startTime">Start Time</Label>
              <Input
                id="startTime"
                type="time"
                value={formData.startTime || ''}
                onChange={(e) => updateFormData('startTime', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endTime">End Time</Label>
              <Input
                id="endTime"
                type="time"
                value={formData.endTime || ''}
                onChange={(e) => updateFormData('endTime', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration">Duration (hours) *</Label>
              <Input
                id="duration"
                type="number"
                step="0.25"
                min="0.25"
                max="24"
                value={formData.duration || ''}
                onChange={(e) => updateFormData('duration', Number(e.target.value))}
                required
              />
            </div>
          </div>

          {/* Project and Task */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Briefcase className="h-4 w-4" />
                Project
              </Label>
              <Select 
                value={selectedProjectId} 
                onValueChange={(value) => {
                  setSelectedProjectId(value);
                  updateFormData('projectId', value);
                  updateFormData('projectName', projects?.find(p => p.id === value)?.name || '');
                  updateFormData('taskId', ''); // Reset task
                  updateFormData('taskName', '');
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select project" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No Project</SelectItem>
                  {projects?.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      <div className="flex items-center gap-2">
                        <span>{project.name}</span>
                        {project.code && (
                          <span className="text-xs text-muted-foreground">
                            ({project.code})
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <CheckSquare className="h-4 w-4" />
                Task
              </Label>
              <Select 
                value={formData.taskId || ''} 
                onValueChange={(value) => {
                  updateFormData('taskId', value);
                  updateFormData('taskName', tasks?.find(t => t.id === value)?.name || '');
                }}
                disabled={!selectedProjectId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select task" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No Task</SelectItem>
                  {tasks?.map((task) => (
                    <SelectItem key={task.id} value={task.id}>
                      {task.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Description and Notes */}
          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea 
              id="description"
              placeholder="Describe what you worked on..."
              className="min-h-20"
              maxLength={500}
              value={formData.description || ''}
              onChange={(e) => updateFormData('description', e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea 
              id="notes"
              placeholder="Additional notes (optional)..."
              className="min-h-20"
              maxLength={1000}
              value={formData.notes || ''}
              onChange={(e) => updateFormData('notes', e.target.value)}
            />
          </div>

          {/* Billing Information */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="billable"
                checked={formData.billable || false}
                onCheckedChange={(checked) => updateFormData('billable', checked)}
              />
              <Label htmlFor="billable">Billable</Label>
            </div>

            {formData.billable && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="hourlyRate" className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Hourly Rate
                  </Label>
                  <Input
                    id="hourlyRate"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    value={formData.hourlyRate || ''}
                    onChange={(e) => updateFormData('hourlyRate', Number(e.target.value))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="totalAmount">Total Amount</Label>
                  <Input
                    id="totalAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    value={formData.totalAmount || ''}
                    onChange={(e) => updateFormData('totalAmount', Number(e.target.value))}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label htmlFor="tags" className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Tags
            </Label>
            <Input
              id="tags"
              placeholder="Enter tags separated by commas"
              value={formData.tags?.join(', ') || ''}
              onChange={(e) => {
                const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                updateFormData('tags', tags);
              }}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button 
              type="submit"
              disabled={createTimeEntry.isPending || updateTimeEntry.isPending}
            >
              <Save className="h-4 w-4 mr-2" />
              {entry ? 'Update Entry' : 'Save Entry'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}