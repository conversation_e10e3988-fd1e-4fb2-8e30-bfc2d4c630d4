{"permissions": {"allow": ["Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(source:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(tree:*)", "Bash(ruff:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(cat:*)", "Bash(ruff check:*)", "Bash(pytest:*)", "<PERSON><PERSON>(python:*)", "Bash(python -m pytest:*)", "Bash(python3 -m pytest:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(pnpm dlx:*)", "Bash(pnpm install:*)", "Bash(pnpm build)", "Bash(pnpm lint:*)", "Bash(pnpm add:*)", "Bash(npx:*)", "Ba<PERSON>(pnpm exec playwright test:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_install", "Bash(pnpm test:*)", "<PERSON><PERSON>(pnpm playwright test:*)", "mcp__playwright__browser_type", "mcp__playwright__browser_click", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_wait_for"], "deny": []}}