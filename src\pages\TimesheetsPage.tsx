import { TimesheetDashboard } from '@/features/timesheets/components/TimesheetDashboard';
import { useAuth } from '@/features/auth/hooks/useAuth';

export function TimesheetsPage() {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">You must be logged in to view timesheets.</p>
        </div>
      </div>
    );
  }

  return (
    <TimesheetDashboard
      userId={user.id}
      entityId={user.entityId}
      userRole={user.roles[0]} // Use first role for simplicity
    />
  );
}