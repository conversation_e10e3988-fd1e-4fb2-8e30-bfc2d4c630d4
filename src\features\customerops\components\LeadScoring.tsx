import { useState } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Filter,
  Download,
  RefreshCw,
  Target,
  Users,
  Award,
  Zap
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

import { useLeads, useLeadPipelineStats } from '../hooks/useLeads';
import { Lead, LeadSearchParams, LeadScore } from '../schemas/leadSchemas';

const scoreColors = {
  hot: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', icon: 'text-red-600' },
  warm: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', icon: 'text-yellow-600' },
  cold: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', icon: 'text-blue-600' },
};

const scoreIcons = {
  hot: TrendingUp,
  warm: Minus,
  cold: TrendingDown,
};

interface LeadScoringProps {
  onViewLead?: (leadId: string) => void;
}

export function LeadScoring({ onViewLead }: LeadScoringProps) {
  const [searchParams] = useState<LeadSearchParams>({
    page: 1,
    limit: 50,
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  });
  const [selectedScore, setSelectedScore] = useState<LeadScore | 'all'>('all');

  const { data: leadsData, isLoading, refetch } = useLeads({
    ...searchParams,
    ...(selectedScore !== 'all' && { score: selectedScore as LeadScore }),
  });
  const { data: pipelineStats } = useLeadPipelineStats();

  const leads = leadsData?.leads || [];

  // Calculate lead scoring metrics
  const scoringMetrics = {
    hot: leads.filter(l => l.score === 'hot').length,
    warm: leads.filter(l => l.score === 'warm').length,
    cold: leads.filter(l => l.score === 'cold').length,
    total: leads.length,
  };

  const hotPercentage = scoringMetrics.total > 0 ? (scoringMetrics.hot / scoringMetrics.total) * 100 : 0;
  const warmPercentage = scoringMetrics.total > 0 ? (scoringMetrics.warm / scoringMetrics.total) * 100 : 0;
  const coldPercentage = scoringMetrics.total > 0 ? (scoringMetrics.cold / scoringMetrics.total) * 100 : 0;

  const getLeadScore = (lead: Lead) => {
    let score = 0;
    
    // Demographics scoring
    if (lead.company) score += 10;
    if (lead.jobTitle && ['ceo', 'cto', 'manager', 'director', 'vp'].some(title => 
      lead.jobTitle!.toLowerCase().includes(title))) score += 15;
    if (lead.industry) score += 5;
    
    // Engagement scoring
    if (lead.email) score += 10;
    if (lead.phone) score += 10;
    if (lead.website) score += 5;
    
    // Qualification scoring (BANT)
    if (lead.budget && lead.budget > 0) score += 20;
    if (lead.authority === 'decision_maker') score += 20;
    if (lead.authority === 'influencer') score += 10;
    if (lead.need) score += 15;
    if (lead.timeline === 'immediate') score += 20;
    if (lead.timeline === 'within_month') score += 15;
    if (lead.timeline === 'within_quarter') score += 10;
    
    // Value scoring
    if (lead.estimatedValue) {
      if (lead.estimatedValue > 100000) score += 25;
      else if (lead.estimatedValue > 50000) score += 20;
      else if (lead.estimatedValue > 10000) score += 15;
      else score += 10;
    }
    
    // Company size scoring
    if (lead.companySize) {
      if (lead.companySize.includes('1000+')) score += 20;
      else if (lead.companySize.includes('501-1000')) score += 15;
      else if (lead.companySize.includes('201-500')) score += 10;
      else score += 5;
    }
    
    return Math.min(score, 100); // Cap at 100
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse bg-muted h-8 w-64 rounded"></div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-32 rounded"></div>
          ))}
        </div>
        <div className="animate-pulse bg-muted h-96 rounded"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Lead Scoring</h2>
          <p className="text-muted-foreground">
            Analyze and prioritize leads based on their qualification score
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Scoring Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4" />
              Total Leads
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{scoringMetrics.total}</div>
            <p className="text-xs text-muted-foreground">Active in pipeline</p>
          </CardContent>
        </Card>

        <Card className="border-red-200 bg-red-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-red-800">
              <Zap className="h-4 w-4" />
              Hot Leads
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-800">{scoringMetrics.hot}</div>
            <div className="flex items-center gap-2">
              <Progress value={hotPercentage} className="flex-1 h-2" />
              <span className="text-xs text-red-700">{hotPercentage.toFixed(1)}%</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-yellow-800">
              <Award className="h-4 w-4" />
              Warm Leads
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-800">{scoringMetrics.warm}</div>
            <div className="flex items-center gap-2">
              <Progress value={warmPercentage} className="flex-1 h-2" />
              <span className="text-xs text-yellow-700">{warmPercentage.toFixed(1)}%</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-blue-200 bg-blue-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2 text-blue-800">
              <Users className="h-4 w-4" />
              Cold Leads
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">{scoringMetrics.cold}</div>
            <div className="flex items-center gap-2">
              <Progress value={coldPercentage} className="flex-1 h-2" />
              <span className="text-xs text-blue-700">{coldPercentage.toFixed(1)}%</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="scoring" className="space-y-4">
        <TabsList>
          <TabsTrigger value="scoring">Lead Scoring</TabsTrigger>
          <TabsTrigger value="distribution">Score Distribution</TabsTrigger>
          <TabsTrigger value="analysis">Conversion Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="scoring" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Select 
                  value={selectedScore} 
                  onValueChange={(value) => setSelectedScore(value as LeadScore | 'all')}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Score" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Scores</SelectItem>
                    <SelectItem value="hot">Hot</SelectItem>
                    <SelectItem value="warm">Warm</SelectItem>
                    <SelectItem value="cold">Cold</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Leads Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Lead</TableHead>
                    <TableHead>Score</TableHead>
                    <TableHead>Calculated Score</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Contact</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {leads.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="text-muted-foreground">
                          No leads found matching your criteria.
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    leads.map((lead) => {
                      const calculatedScore = getLeadScore(lead);
                      const ScoreIcon = scoreIcons[lead.score];
                      const scoreStyle = scoreColors[lead.score];
                      
                      return (
                        <TableRow 
                          key={lead.id} 
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => onViewLead?.(lead.id)}
                        >
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {lead.firstName} {lead.lastName}
                              </div>
                              {lead.email && (
                                <div className="text-sm text-muted-foreground">
                                  {lead.email}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant="outline" 
                              className={`${scoreStyle.bg} ${scoreStyle.text} ${scoreStyle.border} capitalize`}
                            >
                              <ScoreIcon className={`mr-1 h-3 w-3 ${scoreStyle.icon}`} />
                              {lead.score}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Progress value={calculatedScore} className="flex-1 h-2" />
                              <span className="text-sm font-medium w-10">
                                {calculatedScore}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              {lead.company && (
                                <div className="font-medium">{lead.company}</div>
                              )}
                              {lead.jobTitle && (
                                <div className="text-sm text-muted-foreground">
                                  {lead.jobTitle}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {formatCurrency(lead.estimatedValue)}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="capitalize">
                              {lead.status.replace('_', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatDate(lead.lastContactDate)}
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Score Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600">{scoringMetrics.hot}</div>
                    <div className="text-sm text-muted-foreground">Hot Leads (80-100)</div>
                    <Progress value={hotPercentage} className="mt-2" />
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-yellow-600">{scoringMetrics.warm}</div>
                    <div className="text-sm text-muted-foreground">Warm Leads (50-79)</div>
                    <Progress value={warmPercentage} className="mt-2" />
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">{scoringMetrics.cold}</div>
                    <div className="text-sm text-muted-foreground">Cold Leads (0-49)</div>
                    <Progress value={coldPercentage} className="mt-2" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Conversion Analysis by Score</CardTitle>
            </CardHeader>
            <CardContent>
              {pipelineStats ? (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold">{pipelineStats.conversionRate.toFixed(1)}%</div>
                    <div className="text-sm text-muted-foreground">Overall Conversion Rate</div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {['hot', 'warm', 'cold'].map((score) => {
                      const scoreLeads = leads.filter(l => l.score === score);
                      const convertedLeads = scoreLeads.filter(l => l.isConverted);
                      const conversionRate = scoreLeads.length > 0 
                        ? (convertedLeads.length / scoreLeads.length) * 100 
                        : 0;
                      
                      return (
                        <Card key={score} className={scoreColors[score as LeadScore].bg}>
                          <CardContent className="p-4 text-center">
                            <div className={`text-2xl font-bold ${scoreColors[score as LeadScore].text}`}>
                              {conversionRate.toFixed(1)}%
                            </div>
                            <div className={`text-sm ${scoreColors[score as LeadScore].text} capitalize`}>
                              {score} Lead Conversion
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {convertedLeads.length} of {scoreLeads.length} converted
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  Loading conversion analysis...
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}