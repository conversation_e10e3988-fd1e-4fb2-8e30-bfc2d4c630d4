import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '../hooks/useAuth';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface SessionTimeoutProps {
  timeoutMinutes?: number;
  warningMinutes?: number;
}

export function SessionTimeout({ 
  timeoutMinutes = 30, 
  warningMinutes = 5 
}: SessionTimeoutProps) {
  const { isLoggedIn, logout } = useAuth();
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout | number | null>(null);
  const warningRef = useRef<NodeJS.Timeout | number | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | number | null>(null);

  const handleTimeout = useCallback(() => {
    setShowWarning(false);
    logout(() => {});
  }, [logout]);

  const resetTimer = useCallback(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (warningRef.current) clearTimeout(warningRef.current);
    if (countdownRef.current) clearInterval(countdownRef.current);
    setShowWarning(false);

    if (isLoggedIn) {
      // Set warning timer
      warningRef.current = setTimeout(() => {
        setShowWarning(true);
        setTimeLeft(warningMinutes * 60);
        
        // Start countdown
        countdownRef.current = setInterval(() => {
          setTimeLeft(prev => {
            if (prev <= 1) {
              handleTimeout();
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }, (timeoutMinutes - warningMinutes) * 60 * 1000);

      // Set timeout timer
      timeoutRef.current = setTimeout(() => {
        handleTimeout();
      }, timeoutMinutes * 60 * 1000);
    }
  }, [isLoggedIn, timeoutMinutes, warningMinutes, handleTimeout]);

  const extendSession = () => {
    resetTimer();
  };

  useEffect(() => {
    if (isLoggedIn) {
      resetTimer();
    }

    // Reset timer on user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    const resetOnActivity = () => {
      if (isLoggedIn) {
        resetTimer();
      }
    };

    events.forEach(event => {
      document.addEventListener(event, resetOnActivity, true);
    });

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (warningRef.current) clearTimeout(warningRef.current);
      if (countdownRef.current) clearInterval(countdownRef.current);
      events.forEach(event => {
        document.removeEventListener(event, resetOnActivity, true);
      });
    };
  }, [isLoggedIn, resetTimer]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <AlertDialog open={showWarning} onOpenChange={setShowWarning}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Session Expiring</AlertDialogTitle>
          <AlertDialogDescription>
            Your session will expire in {formatTime(timeLeft)}. 
            Would you like to extend your session?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleTimeout}>
            Logout
          </AlertDialogCancel>
          <AlertDialogAction onClick={extendSession}>
            Extend Session
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}