import { useState } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { 
  Plus, 
  Filter, 
  Eye, 
  Edit, 
  Trash2, 
  User, 
  Calendar, 
  DollarSign,
  Phone,
  Mail,
  Building,
  MoreHorizontal,
  Percent,
  TrendingUp,
  Clock,
  Target
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { RoleGuard } from '@/features/auth/components/RoleGuard';
import { PERMISSIONS } from '@/features/auth/schemas/authSchemas';
import { useDeals, useUpdateDeal, useDeleteDeal, useDealPipelineStats } from '../hooks/useDeals';
import { 
  Deal, 
  DealStatus,
  DealPriority,
  DealSearchParams 
} from '../schemas/dealSchemas';

const statusConfig: Record<DealStatus, { title: string; color: string }> = {
  prospecting: { title: 'Prospecting', color: 'bg-blue-100 text-blue-800 border-blue-200' },
  qualification: { title: 'Qualification', color: 'bg-purple-100 text-purple-800 border-purple-200' },
  proposal: { title: 'Proposal', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  negotiation: { title: 'Negotiation', color: 'bg-orange-100 text-orange-800 border-orange-200' },
  closed_won: { title: 'Closed Won', color: 'bg-green-100 text-green-800 border-green-200' },
  closed_lost: { title: 'Closed Lost', color: 'bg-red-100 text-red-800 border-red-200' },
  on_hold: { title: 'On Hold', color: 'bg-gray-100 text-gray-800 border-gray-200' },
};

const priorityColors = {
  low: 'bg-green-500',
  medium: 'bg-yellow-500',
  high: 'bg-orange-500',
  critical: 'bg-red-500',
};

interface DealPipelineProps {
  onCreateDeal?: () => void;
  onEditDeal?: (deal: Deal) => void;
  onViewDeal?: (deal: Deal) => void;
  onCreateQuote?: (deal: Deal) => void;
}

export function DealPipeline({ onCreateDeal, onEditDeal, onViewDeal, onCreateQuote }: DealPipelineProps) {
  const [searchParams, setSearchParams] = useState<DealSearchParams>({
    page: 1,
    limit: 100,
    sortBy: 'updatedAt',
    sortOrder: 'desc',
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [dealToDelete, setDealToDelete] = useState<Deal | null>(null);

  const { data: dealsData, isLoading } = useDeals(searchParams);
  const { data: pipelineStats } = useDealPipelineStats();
  const updateDeal = useUpdateDeal();
  const deleteDeal = useDeleteDeal();

  // Group deals by status
  const groupedDeals: Record<DealStatus, Deal[]> = dealsData?.deals.reduce((acc, deal) => {
    const status = deal.status as DealStatus;
    if (!acc[status]) {
      acc[status] = [];
    }
    acc[status].push(deal);
    return acc;
  }, {} as Record<DealStatus, Deal[]>) || {
    prospecting: [],
    qualification: [],
    proposal: [],
    negotiation: [],
    closed_won: [],
    closed_lost: [],
    on_hold: [],
  };

  const handleDragEnd = async (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    const dealId = draggableId;
    const newStatus = destination.droppableId as DealStatus;

    try {
      await updateDeal.mutateAsync({
        dealId,
        deal: { status: newStatus },
      });
    } catch (error) {
      console.error('Failed to update deal status:', error);
    }
  };

  const handleDeleteDeal = async () => {
    if (!dealToDelete) return;
    
    try {
      await deleteDeal.mutateAsync(dealToDelete.id);
      setDeleteDialogOpen(false);
      setDealToDelete(null);
    } catch (error) {
      console.error('Failed to delete deal:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const DealCard = ({ deal, index }: { deal: Deal; index: number }) => (
    <Draggable draggableId={deal.id} index={index}>
      {(provided, snapshot) => (
        <Card
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`mb-3 cursor-move transition-shadow ${
            snapshot.isDragging ? 'shadow-lg' : 'hover:shadow-md'
          }`}
        >
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm">{deal.title}</h4>
                  <div className={`w-2 h-2 rounded-full ${priorityColors[deal.priority]}`} title={`${deal.priority} priority`} />
                </div>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <Building className="h-3 w-3" />
                  {deal.customerName}
                </p>
                {deal.assignedUserName && (
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <User className="h-3 w-3" />
                    {deal.assignedUserName}
                  </p>
                )}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => onViewDeal?.(deal)}>
                    <Eye className="mr-2 h-3 w-3" />
                    View Details
                  </DropdownMenuItem>
                  <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
                    <DropdownMenuItem onClick={() => onEditDeal?.(deal)}>
                      <Edit className="mr-2 h-3 w-3" />
                      Edit Deal
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onCreateQuote?.(deal)}>
                      <DollarSign className="mr-2 h-3 w-3" />
                      Create Quote
                    </DropdownMenuItem>
                  </RoleGuard>
                  <DropdownMenuItem>
                    <Phone className="mr-2 h-3 w-3" />
                    Call Customer
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Mail className="mr-2 h-3 w-3" />
                    Send Email
                  </DropdownMenuItem>
                  <RoleGuard permissions={[PERMISSIONS['customerops:delete']]}>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-red-600"
                      onClick={() => {
                        setDealToDelete(deal);
                        setDeleteDialogOpen(true);
                      }}
                    >
                      <Trash2 className="mr-2 h-3 w-3" />
                      Delete Deal
                    </DropdownMenuItem>
                  </RoleGuard>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="space-y-2 text-xs">
              <div className="flex items-center gap-1">
                <DollarSign className="h-3 w-3 text-muted-foreground" />
                <span className="font-medium">{formatCurrency(deal.value)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Percent className="h-3 w-3 text-muted-foreground" />
                <span>{deal.probability}% probability</span>
              </div>
              {deal.expectedCloseDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3 text-muted-foreground" />
                  <span>{formatDate(deal.expectedCloseDate)}</span>
                </div>
              )}
            </div>

            <div className="mt-2">
              <Progress value={deal.probability} className="h-2" />
            </div>

            {deal.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {deal.tags.slice(0, 2).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                    {tag}
                  </Badge>
                ))}
                {deal.tags.length > 2 && (
                  <Badge variant="secondary" className="text-xs px-1 py-0">
                    +{deal.tags.length - 2}
                  </Badge>
                )}
              </div>
            )}

            {deal.quotes.length > 0 && (
              <div className="mt-2 pt-2 border-t">
                <div className="text-xs text-muted-foreground">
                  {deal.quotes.length} quote{deal.quotes.length > 1 ? 's' : ''}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </Draggable>
  );

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="animate-pulse bg-muted h-8 w-64 rounded"></div>
          <div className="animate-pulse bg-muted h-10 w-32 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-32 rounded"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-7 gap-4">
          {Array.from({ length: 7 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-96 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Deal Pipeline</h2>
          <p className="text-muted-foreground">
            Manage and track deals through your sales process
          </p>
        </div>
        <div className="flex gap-2">
          <Select 
            value={searchParams.priority || 'all'} 
            onValueChange={(value) => setSearchParams(prev => ({ 
              ...prev, 
              priority: value === 'all' ? undefined : value as DealPriority 
            }))}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <RoleGuard permissions={[PERMISSIONS['customerops:write']]}>
            <Button onClick={onCreateDeal}>
              <Plus className="mr-2 h-4 w-4" />
              Add Deal
            </Button>
          </RoleGuard>
        </div>
      </div>

      {/* Pipeline Stats */}
      {pipelineStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Target className="h-4 w-4" />
                Total Deals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pipelineStats.totalDeals}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Conversion Rate
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pipelineStats.conversionRate.toFixed(1)}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Pipeline Value
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(pipelineStats.totalValue)}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Avg. Time to Close
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pipelineStats.averageTimeToClose} days</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Pipeline Board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4">
          {(Object.keys(statusConfig) as DealStatus[]).map((status) => {
            const config = statusConfig[status];
            const statusDeals = groupedDeals[status] || [];
            
            return (
              <div key={status} className="flex flex-col">
                <div className={`p-3 rounded-t-lg border-b-2 ${config.color}`}>
                  <h3 className="font-medium text-sm">{config.title}</h3>
                  <p className="text-xs opacity-75">{statusDeals.length} deals</p>
                  <p className="text-xs opacity-75">
                    {formatCurrency(statusDeals.reduce((sum, deal) => sum + deal.value, 0))}
                  </p>
                </div>
                
                <Droppable droppableId={status}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`flex-1 p-2 min-h-[400px] bg-muted/20 rounded-b-lg border-l border-r border-b transition-colors ${
                        snapshot.isDraggingOver ? 'bg-muted/40' : ''
                      }`}
                    >
                      {statusDeals.map((deal, index) => (
                        <DealCard key={deal.id} deal={deal} index={index} />
                      ))}
                      {provided.placeholder}
                      
                      {statusDeals.length === 0 && (
                        <div className="flex items-center justify-center h-32 text-muted-foreground text-sm">
                          No deals in this stage
                        </div>
                      )}
                    </div>
                  )}
                </Droppable>
              </div>
            );
          })}
        </div>
      </DragDropContext>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Deal</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{dealToDelete?.title}"? 
              This action cannot be undone and will also delete all associated quotes.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteDeal}
              disabled={deleteDeal.isPending}
            >
              {deleteDeal.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}